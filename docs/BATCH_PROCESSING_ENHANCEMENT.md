# Streaming Batch Processing with Parallel Execution and Thread Limiting

## Overview

This document describes the streaming batch processing implementation in `UnificationNew.scala` that provides:

1. **Streaming data processing** - Fetches data in batches instead of loading all at once
2. **Parallel execution** with thread pool management for each batch
3. **Thread limiting** to control resource usage and prevent database overload
4. **Memory-efficient processing** - Processes data in chunks to avoid memory issues
5. **Improved error handling** with retry mechanisms and graceful failure recovery
6. **Pagination support** - Continues fetching and processing until all data is consumed

## Key Features

### 1. Thread Pool Management

- **Configurable thread pool**: Maximum number of threads can be configured via `unification.max.threads` property
- **Automatic cleanup**: Thread pool is properly shutdown after processing
- **Resource control**: Prevents overwhelming the database with too many concurrent connections

### 2. Streaming Batch Processing

- **Pagination-based fetching**: Data is fetched in configurable batches (default: 10,000 records)
- **Memory-efficient**: Only one batch is loaded in memory at a time
- **Continuous processing**: Automatically fetches next batch until all data is processed
- **Database operation batches**: Each fetched batch is further divided for efficient DB operations (default: 100 records)
- **Parallel processing**: Each batch is processed in parallel using thread pool
- **Configurable batch sizes**: All batch sizes can be adjusted via configuration

### 3. Improved Error Handling

- **Retry mechanism**: Failed operations are automatically retried (default: 3 attempts)
- **Graceful degradation**: Individual batch failures don't stop the entire process
- **Comprehensive logging**: Detailed logging for monitoring and debugging

## Configuration Properties

Add these properties to `application.properties`:

```properties
# Unification streaming batch processing configuration
unification.batch.size=10000          # Size of each data batch fetched from database
unification.db.batch.size=100         # Size of database operation batches
unification.max.threads=8             # Maximum number of parallel threads for processing
unification.max.retries=3             # Number of retry attempts for failed operations
unification.connection.timeout=30000  # Database connection timeout in milliseconds
unification.processing.timeout=30     # Batch processing timeout in minutes
```

## Architecture

### Processing Flow

1. **Streaming Data Fetching**: Raw data is fetched from Doris in configurable batches using pagination
2. **Batch Processing Loop**: Continue fetching and processing until no more data is available
3. **Parallel Processing**: Each fetched batch is processed in parallel using thread pool
4. **Database Operation Batching**: Within each batch, data is divided into smaller database operation batches
5. **Concurrent Database Operations**: Database batches are processed concurrently within thread limits
6. **Error Handling**: Failed operations are retried with exponential backoff
7. **Memory Management**: Only one batch is kept in memory at a time

### Streaming Processing Architecture

```
Streaming Batch Processing Loop
    ├── Fetch Batch 1 (10,000 records) ──┐
    ├── Fetch Batch 2 (10,000 records) ──┤
    └── Fetch Batch N (remaining)     ──┤
                                         │
                                         ├── Thread Pool (max 8 threads)
    Each Batch Processing                │
    ├── DB Operation Batch 1 (100) ──┤
    ├── DB Operation Batch 2 (100) ──┤
    └── DB Operation Batch N (100) ──┘

    Memory Usage: Only 1 batch in memory at a time
    Processing: Parallel within each batch
```

## Enhanced Methods

### 1. `appendProfileEnhanced`

- **Purpose**: Insert new profiles with parallel batch processing
- **Features**:
  - Concurrent database operations within thread limits
  - Transaction management with rollback on failure
  - Automatic retry on failure
  - Kafka publishing for each successful batch

### 2. `mergeProfileEnhanced`

- **Purpose**: Update existing profiles with parallel batch processing
- **Features**:
  - Batch fetching of existing profiles
  - Concurrent merge operations
  - Field-level data merging with source tracking
  - Transactional updates with rollback support

### 3. `withRetry`

- **Purpose**: Generic retry mechanism for any operation
- **Features**:
  - Configurable retry attempts
  - Exponential backoff between retries
  - Comprehensive error logging

## Performance Benefits

### Before Enhancement

- **Single-threaded database operations**: One connection per Spark partition
- **Large batch operations**: Risk of memory issues and long-running transactions
- **Limited error recovery**: Failed batches could stop entire process
- **Fixed batch sizes**: No flexibility for different workloads

### After Enhancement

- **Multi-threaded database operations**: Configurable thread pool for concurrent operations
- **Optimized batch sizes**: Two-level batching for better memory management
- **Robust error handling**: Retry mechanisms and graceful failure handling
- **Configurable parameters**: Tunable for different workloads and environments

## Performance Tuning Guidelines

### Batch Size Configuration

- **Large datasets (>1M records)**:
  - `unification.batch.size=20000`
  - `unification.db.batch.size=200`
- **Medium datasets (100K-1M records)**:
  - `unification.batch.size=10000`
  - `unification.db.batch.size=100`
- **Small datasets (<100K records)**:
  - `unification.batch.size=5000`
  - `unification.db.batch.size=50`

### Thread Pool Configuration

- **High-performance environments**: `unification.max.threads=16`
- **Standard environments**: `unification.max.threads=8`
- **Resource-constrained environments**: `unification.max.threads=4`

### Database Configuration

- **Connection timeout**: Adjust based on network latency
- **Retry attempts**: Increase for unstable network conditions

## Monitoring and Debugging

### Key Metrics to Monitor

1. **Processing time per batch**
2. **Thread pool utilization**
3. **Database connection usage**
4. **Retry frequency**
5. **Memory usage patterns**

### Log Messages

- `INFO`: Batch processing progress and completion
- `DEBUG`: Detailed operation logs (enable for troubleshooting)
- `WARN`: Retry attempts and recoverable errors
- `ERROR`: Unrecoverable errors and failures

## Best Practices

1. **Start with default configurations** and adjust based on performance monitoring
2. **Monitor database connection pools** to avoid exhaustion
3. **Use appropriate batch sizes** based on available memory
4. **Enable debug logging** during initial deployment for monitoring
5. **Set up alerts** for high retry rates or processing failures

## Backward Compatibility

The enhanced implementation maintains backward compatibility:

- **Existing methods**: Original methods are preserved
- **Default behavior**: Uses enhanced methods with fallback to original methods
- **Configuration**: New configurations have sensible defaults
- **API compatibility**: No changes to public method signatures

## Future Enhancements

Potential improvements for future versions:

1. **Dynamic batch sizing**: Adjust batch sizes based on processing performance
2. **Connection pooling**: Implement database connection pooling
3. **Metrics collection**: Built-in performance metrics collection
4. **Circuit breaker**: Automatic failure detection and recovery
5. **Streaming processing**: Support for real-time data streaming
