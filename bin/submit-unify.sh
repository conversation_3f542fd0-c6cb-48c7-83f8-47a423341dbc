#!/usr/bin/env bash
class="com.vcc.bigdata.application.launcher.UnificationNewLauncher"
deployMode="client"
serviceName="UnificationNewLauncher-Local"
jarPath="target/spark-job-codebase-1.0-jar-with-dependencies.jar"

# Check for compatible Java version
# Spark works best with Java 8 or 11
if [ -n "$JAVA_8_HOME" ]; then
  echo "Using Java 8 from JAVA_8_HOME: $JAVA_8_HOME"
  export JAVA_HOME=$JAVA_8_HOME
elif [ -n "$JAVA_11_HOME" ]; then
  echo "Using Java 11 from JAVA_11_HOME: $JAVA_11_HOME"
  export JAVA_HOME=$JAVA_11_HOME
else
  java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F. '{print $1}')
  if [ "$java_version" -gt "11" ]; then
    echo "WARNING: You are using Java version $java_version which may not be compatible with Spark."
    echo "It is recommended to use Java 8 or 11 for Spark applications."
    echo "Set JAVA_8_HOME or JAVA_11_HOME environment variables to point to a compatible JDK."
  fi
fi

KMS_JAR_FILE=kms-client-1.0.34.jar
if [ ! -f "$KMS_JAR_FILE" ]; then
  wget https://maven.admicro.vn/nexus/content/repositories/ml-group/com/vcc/bigdata/kms-client/1.0.34/kms-client-1.0.34.jar
fi

# Check if unificationId is provided
unificationId=${1:-"1374793718190120960"}  # Default value if not provided
testMode="true"  # Set to true for local testing

spark-submit --class "$class" \
  --master local[*] \
  --deploy-mode "$deployMode" \
  --driver-memory 5g \
  --executor-memory 5g \
  --name "$serviceName" \
  --conf "spark.executor.extraJavaOptions=-Des.set.netty.runtime.available.processors=false" \
  --conf "spark.driver.extraJavaOptions=-Dfile.encoding=utf-8" \
  --jars "$KMS_JAR_FILE" \
  "$jarPath" \
  "$testMode" "$unificationId"

echo "Local execution completed."
