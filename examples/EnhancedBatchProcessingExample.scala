package examples

import com.vcc.bigdata.application.build.UnificationNew
import com.vcc.bigdata.connector.kafka.UnifyDataMessagePublisher
import org.json.JSONObject

/**
 * Example demonstrating the streaming batch processing capabilities
 * of the UnificationNew class with parallel execution and thread limiting.
 *
 * This approach fetches data in batches and processes them in parallel
 * until all data is consumed, providing memory-efficient processing.
 */
object EnhancedBatchProcessingExample {

  def main(args: Array[String]): Unit = {
    println("Streaming Batch Processing Example")
    println("===================================")

    // Initialize the UnificationNew instance
    val unificationNew = new UnificationNew()

    // Configuration example
    demonstrateConfiguration()

    // Initialize with test parameters
    unificationNew.init(Array("true", "example-unification-id"))

    // Example 1: Basic usage with default settings
    basicUsageExample(unificationNew)

    // Example 2: Custom configuration for high-volume processing
    highVolumeProcessingExample(unificationNew)

    // Example 3: Streaming batch processing demonstration
    streamingBatchProcessingExample(unificationNew)

    // Example 4: Error handling and retry demonstration
    errorHandlingExample(unificationNew)

    println("Example completed successfully!")
  }

  /**
   * Demonstrate configuration options
   */
  def demonstrateConfiguration(): Unit = {
    println("\n1. Configuration Options")
    println("------------------------")

    println("Add these properties to application.properties:")
    println("unification.batch.size=10000          # Size of each data batch fetched")
    println("unification.db.batch.size=100         # Database operation batch size")
    println("unification.max.threads=8             # Maximum parallel threads")
    println("unification.max.retries=3             # Retry attempts")
    println("unification.connection.timeout=30000  # Connection timeout")
    println("unification.processing.timeout=30     # Batch processing timeout")
  }

  /**
   * Basic usage example with default settings
   */
  def basicUsageExample(unificationNew: UnificationNew): Unit = {
    println("\n2. Basic Usage Example")
    println("----------------------")

    // Create sample profile data
    val sampleProfiles = createSampleProfileData(1000)
    println(s"Created ${sampleProfiles.size} sample profiles")

    // Mock Kafka publisher for demonstration
    val mockKafkaPublisher = createMockKafkaPublisher()

    try {
      // Use enhanced append profile method
      println("Processing profiles with enhanced batch processing...")

      // This would normally be called within the UnificationNew.run() method
      // but shown here for demonstration purposes
      /*
      unificationNew.appendProfileEnhanced(
        userId = "example-user-123",
        dataTypeMap = Map("name" -> "string", "email" -> "string", "phone" -> "string"),
        profileData = sampleProfiles,
        sourceId = "example-source-456",
        unifyDataKafkaPublisher = mockKafkaPublisher,
        batchSize = 100 // Custom batch size for this operation
      )
      */

      println("✓ Profiles processed successfully with parallel batch processing")

    } catch {
      case e: Exception =>
        println(s"✗ Error processing profiles: ${e.getMessage}")
    }
  }

  /**
   * High-volume processing example with custom configuration
   */
  def highVolumeProcessingExample(unificationNew: UnificationNew): Unit = {
    println("\n3. High-Volume Processing Example")
    println("---------------------------------")

    // For high-volume processing, you might want to:
    // 1. Increase batch sizes
    // 2. Use more threads
    // 3. Adjust retry settings

    println("Recommended settings for high-volume processing:")
    println("- unification.batch.size=20000")
    println("- unification.db.batch.size=200")
    println("- unification.max.threads=16")
    println("- Monitor memory usage and database connections")

    // Create larger dataset
    val largeDataset = createSampleProfileData(50000)
    println(s"Created ${largeDataset.size} profiles for high-volume processing")

    // Processing would be similar to basic example but with larger batches
    println("✓ High-volume processing configuration demonstrated")
  }

  /**
   * Streaming batch processing demonstration
   */
  def streamingBatchProcessingExample(unificationNew: UnificationNew): Unit = {
    println("\n3. Streaming Batch Processing Example")
    println("-------------------------------------")

    println("Streaming batch processing flow:")
    println("1. Fetch batch 1 (10,000 records) from database")
    println("2. Process batch 1 in parallel with thread limiting")
    println("3. Fetch batch 2 (10,000 records) from database")
    println("4. Process batch 2 in parallel with thread limiting")
    println("5. Continue until no more data is available")

    println("\nKey benefits:")
    println("✓ Memory efficient - only one batch in memory at a time")
    println("✓ Parallel processing - each batch processed with thread pool")
    println("✓ Scalable - handles datasets of any size")
    println("✓ Fault tolerant - individual batch failures don't stop processing")

    // Simulate streaming batch processing
    val totalRecords = 50000
    val batchSize = 10000
    val numBatches = (totalRecords + batchSize - 1) / batchSize

    println(s"\nSimulating processing of $totalRecords records in $numBatches batches:")

    for (batchNum <- 1 to numBatches) {
      val startOffset = (batchNum - 1) * batchSize
      val actualBatchSize = Math.min(batchSize, totalRecords - startOffset)

      println(s"Batch $batchNum: Fetching $actualBatchSize records (offset: $startOffset)")

      // Simulate processing time
      Thread.sleep(100)

      println(s"Batch $batchNum: Processing completed")
    }

    println("✓ Streaming batch processing simulation completed")
  }

  /**
   * Error handling and retry demonstration
   */
  def errorHandlingExample(unificationNew: UnificationNew): Unit = {
    println("\n4. Error Handling and Retry Example")
    println("-----------------------------------")

    // Demonstrate retry mechanism
    var attemptCount = 0

    val result = unificationNew.withRetry({
      attemptCount += 1
      println(s"Attempt $attemptCount")

      if (attemptCount < 3) {
        throw new RuntimeException("Simulated temporary failure")
      }

      "Operation succeeded after retries"
    }, maxAttempts = 3)

    result match {
      case scala.util.Success(value) =>
        println(s"✓ Operation succeeded: $value")
        println(s"✓ Total attempts: $attemptCount")
      case scala.util.Failure(exception) =>
        println(s"✗ Operation failed: ${exception.getMessage}")
    }
  }

  /**
   * Create sample profile data for demonstration
   */
  def createSampleProfileData(count: Int): List[Map[String, List[Object]]] = {
    (1 to count).map { i =>
      Map(
        "name" -> List(s"User $i"),
        "email" -> List(s"user$<EMAIL>"),
        "phone" -> List(s"******-${String.format("%04d", i)}"),
        "company" -> List(s"Company ${i % 100}"),
        "city" -> List(cities(i % cities.length))
      )
    }.toList
  }

  /**
   * Sample cities for generating diverse data
   */
  val cities = Array(
    "New York", "Los Angeles", "Chicago", "Houston", "Phoenix",
    "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"
  )

  /**
   * Create a mock Kafka publisher for demonstration
   */
  def createMockKafkaPublisher(): UnifyDataMessagePublisher = {
    // In a real implementation, this would be properly configured
    // For demonstration, we'll create a simple mock
    new UnifyDataMessagePublisher(null, null) {
      override def publish(message: String): Unit = {
        // Mock implementation - just log the message
        println(s"[KAFKA] Published: ${message.take(100)}...")
      }
    }
  }

  /**
   * Performance monitoring example
   */
  def performanceMonitoringExample(): Unit = {
    println("\n5. Performance Monitoring")
    println("-------------------------")

    println("Key metrics to monitor:")
    println("- Processing time per batch")
    println("- Thread pool utilization")
    println("- Database connection usage")
    println("- Memory consumption")
    println("- Retry frequency")

    println("\nExample monitoring code:")
    println("""
      val startTime = System.currentTimeMillis()
      // ... processing ...
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime

      logger.info(s"Processed $batchSize records in ${processingTime}ms")
      logger.info(s"Throughput: ${batchSize * 1000 / processingTime} records/second")
    """)
  }

  /**
   * Best practices summary
   */
  def bestPracticesSummary(): Unit = {
    println("\n6. Best Practices Summary")
    println("-------------------------")

    println("✓ Start with default configurations")
    println("✓ Monitor database connection pools")
    println("✓ Use appropriate batch sizes for available memory")
    println("✓ Enable debug logging during initial deployment")
    println("✓ Set up alerts for high retry rates")
    println("✓ Test with representative data volumes")
    println("✓ Monitor thread pool utilization")
    println("✓ Implement proper error handling")
  }
}
