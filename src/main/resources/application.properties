# HBASE
hbase.zookeeper.quorum=10.5.37.230,10.5.38.27,10.5.38.161
hbase.zookeeper.property.clientPort=2181
hbase.zookeeper.znode.parent=/hbase-unsecure
hbase.zookeeper.namespace=cdp
hbase.prefix.raw_data=cdp:raw_data_
hbase.prefix.person=cdp:profile_person_
# Spark
spark.yarn.queue=datacollection

# Unification batch processing configuration
unification.batch.size=10000
unification.db.batch.size=100
unification.max.threads=8
unification.max.retries=3
unification.connection.timeout=30000
# API
source-design.update=http://***********:8000/mlbigdata/cdp/dev/customer-interface/source-design/update
source-mapping.get=http://***********:8000/mlbigdata/cdp/dev/customer-interface/source/get-mapping
unification.update=http://***********:8000/mlbigdata/cdp/dev/customer-interface/unification/update
unification.success=http://***********:8000/mlbigdata/cdp/dev/customer-interface/unification/unification-success
profile_management.update.status=http://***********:8000/mlbigdata/cdp/dev/customer-interface/profile-management/update-status
source-design.remove-block-row-key=http://***********:8000/mlbigdata/cdp/dev/rawdata-ingestion/source/delete-row-key
source-design.insert-block-row-key=http://***********:8000/mlbigdata/cdp/dev/rawdata-ingestion/source/insert-row-key
profile_management.get=http://***********:8000/mlbigdata/cdp/dev/customer-interface/profile-management/get-by-hashing
field.detect.get=http://***********:8000/mlbigdata/cdp/dev/customer-interface/unification/field-suggestion-by-name
kafka.rawdata.topic=ml-cdp-data-loading
api.guid.age_gender=http://***********:8092/uv
# ElasticSearch
es.hosts=es-cdp.sys.adt.internal
#es.hosts=***********
es.port=9200
es.user=cdp-dev
es.pass=FT0JQYaOgS5mCRX6LuxE
management.health.elasticsearch.enabled=false
es.prefix.profile.person=dev-cdp-profile-person-
# KMS
kms.keystore_base64=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
kms.truststore_base64=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
kms.password_file=01GWVEKJ342BTCQZQFHWRP8MT1
kms.domain=https://dev.kms.admicro.vn:31087
kms.key.decrypt_key=ahI30u94x2Sj6YOc24SIdQ==

#tidb
tidb.datasource.url=jdbc:mysql://***********:4000/cdp_beta?enabledTLSProtocols=TLSv1.2,TLSv1.3&autoReconnect=true
tidb.datasource.username=cdp-unify-batch
tidb.datasource.password=P4q7cuydJr2FXNQDWIsn
tidb.paging.rawdata=paging_rawdata_

# Kafka
kafka.publisher.bootstrap-servers=kafka-2dc-c2.sys.adt.internal:9093
kafka.publisher.unify-topic=ml-cdp-unify-profile
kafka.publisher.username=ml-cdp
kafka.publisher.password=5sGXcGF6oGSyC3tN7

# doris
doris.datasource.url=*******************************************************************,TLSv1.3&autoReconnect=true
doris.datasource.username=cdp
doris.datasource.password=cdp
doris.fenodes=10.3.104.125:8030
doris.database=cdp_beta
doris.raw_data_table=raw_data_
doris.profile_person_table=profile_person_
# Additional doris properties needed by UnificationNew
doris.table=raw_data_1839449391_1372258122060783616
doris.user=cdp
doris.password=cdp
