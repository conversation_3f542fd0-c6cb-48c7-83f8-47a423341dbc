//package com.vcc.bigdata.connector
//
//import com.typesafe.scalalogging.Logger
//import com.vcc.bigdata.config.AppConfig
//import com.zaxxer.hikari.{HikariConfig, HikariDataSource}
//
//import java.sql.{Connection, PreparedStatement, ResultSet, SQLException}
//import scala.util.{Failure, Success, Try}
//
///**
// * Connection pool manager for Doris database connections
// */
//object DorisConnectionPool {
//  private val logger = Logger(getClass)
//  private var dataSource: HikariDataSource = _
//
//  /**
//   * Initialize the connection pool
//   */
//  def init(): Unit = {
//    if (dataSource == null) {
//      synchronized {
//        if (dataSource == null) {
//          val config = new HikariConfig()
//          val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
//
//          config.setJdbcUrl(jdbcUrl)
//          config.setUsername(AppConfig.getProperties("doris.user"))
//          config.setPassword(AppConfig.getProperties("doris.password"))
//          config.setDriverClassName("com.mysql.jdbc.Driver")
//
//          // Connection pool settings
//          config.setMaximumPoolSize(10)
//          config.setMinimumIdle(5)
//          config.setIdleTimeout(30000)
//          config.setConnectionTimeout(30000)
//          config.setMaxLifetime(1800000)
//
//          // Connection test query
//          config.setConnectionTestQuery("SELECT 1")
//
//          // Pool name
//          config.setPoolName("DorisHikariCP")
//
//          dataSource = new HikariDataSource(config)
//          logger.info("Initialized Doris connection pool")
//        }
//      }
//    }
//  }
//
//  /**
//   * Get a connection from the pool
//   * @return Connection
//   */
//  def getConnection: Connection = {
//    if (dataSource == null) {
//      init()
//    }
//    dataSource.getConnection
//  }
//
//  /**
//   * Execute a query with a prepared statement
//   * @param sql SQL query
//   * @param params Parameters for the prepared statement
//   * @param resultHandler Function to handle the result set
//   * @tparam T Return type
//   * @return Result of the query
//   */
//  def executeQuery[T](sql: String, params: Seq[Any] = Seq.empty)(resultHandler: ResultSet => T): Try[T] = {
//    var connection: Connection = null
//    var preparedStatement: PreparedStatement = null
//    var resultSet: ResultSet = null
//
//    try {
//      connection = getConnection
//      preparedStatement = connection.prepareStatement(sql)
//
//      // Set parameters
//      params.zipWithIndex.foreach { case (param, index) =>
//        preparedStatement.setObject(index + 1, param)
//      }
//
//      resultSet = preparedStatement.executeQuery()
//      Success(resultHandler(resultSet))
//    } catch {
//      case e: SQLException =>
//        logger.error(s"SQL error executing query: $sql", e)
//        Failure(e)
//      case e: Exception =>
//        logger.error(s"Error executing query: $sql", e)
//        Failure(e)
//    } finally {
//      if (resultSet != null) Try(resultSet.close())
//      if (preparedStatement != null) Try(preparedStatement.close())
//      if (connection != null) Try(connection.close())
//    }
//  }
//
//  /**
//   * Execute an update with a prepared statement
//   * @param sql SQL query
//   * @param params Parameters for the prepared statement
//   * @return Number of rows affected
//   */
//  def executeUpdate(sql: String, params: Seq[Any] = Seq.empty): Try[Int] = {
//    var connection: Connection = null
//    var preparedStatement: PreparedStatement = null
//
//    try {
//      connection = getConnection
//      preparedStatement = connection.prepareStatement(sql)
//
//      // Set parameters
//      params.zipWithIndex.foreach { case (param, index) =>
//        preparedStatement.setObject(index + 1, param)
//      }
//
//      Success(preparedStatement.executeUpdate())
//    } catch {
//      case e: SQLException =>
//        logger.error(s"SQL error executing update: $sql", e)
//        Failure(e)
//      case e: Exception =>
//        logger.error(s"Error executing update: $sql", e)
//        Failure(e)
//    } finally {
//      if (preparedStatement != null) Try(preparedStatement.close())
//      if (connection != null) Try(connection.close())
//    }
//  }
//
//  /**
//   * Execute a batch update with a prepared statement
//   * @param sql SQL query
//   * @param batchParams List of parameter batches
//   * @return Array of rows affected for each batch
//   */
//  def executeBatch(sql: String, batchParams: Seq[Seq[Any]]): Try[Array[Int]] = {
//    var connection: Connection = null
//    var preparedStatement: PreparedStatement = null
//
//    try {
//      connection = getConnection
//      connection.setAutoCommit(false)
//      preparedStatement = connection.prepareStatement(sql)
//
//      // Add batches
//      batchParams.foreach { params =>
//        params.zipWithIndex.foreach { case (param, index) =>
//          preparedStatement.setObject(index + 1, param)
//        }
//        preparedStatement.addBatch()
//      }
//
//      val result = preparedStatement.executeBatch()
//      connection.commit()
//      Success(result)
//    } catch {
//      case e: SQLException =>
//        if (connection != null) Try(connection.rollback())
//        logger.error(s"SQL error executing batch: $sql", e)
//        Failure(e)
//      case e: Exception =>
//        if (connection != null) Try(connection.rollback())
//        logger.error(s"Error executing batch: $sql", e)
//        Failure(e)
//    } finally {
//      if (preparedStatement != null) Try(preparedStatement.close())
//      if (connection != null) {
//        Try(connection.setAutoCommit(true))
//        Try(connection.close())
//      }
//    }
//  }
//
//  /**
//   * Close the connection pool
//   */
//  def close(): Unit = {
//    if (dataSource != null) {
//      dataSource.close()
//      dataSource = null
//      logger.info("Closed Doris connection pool")
//    }
//  }
//}
