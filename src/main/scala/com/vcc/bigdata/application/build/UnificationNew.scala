package com.vcc.bigdata.application.build

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.typesafe.scalalogging.Logger
import com.vcc.bigdata.application.model.{FieldSuggestData, SourceMapping, Unification}
import com.vcc.bigdata.config.AppConfig
import com.vcc.bigdata.connector.kafka.{KafkaConfiguration, KafkaPublisherProperties, UnifyDataMessagePublisher}
import com.vcc.bigdata.infrastructure.APIHelper
import com.vcc.bigdata.utility.Strings
import org.apache.spark.sql.{SQLContext, SparkSession}
import org.apache.spark.{SparkConf, SparkContext}
import org.json.{JSONArray, JSONObject}

import java.sql.DriverManager
import java.util.concurrent.{Executors, ThreadPoolExecutor, TimeUnit}
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/*
 * This class is intended for testing the new unification process
 * which involves storing data only in apache doris as a replacement for
 * the current data storage systems (HBase, Elasticsearch, TiDB).
 */
class UnificationNew extends Serializable {
  // Initialize the necessary configurations and resources
  System.setProperty("HADOOP_USER_NAME", "cdp")

  val logger: Logger = Logger(classOf[UnificationNew])

  var sparkConf: SparkConf = _
  @transient
  var sqlContext: SQLContext = _
  @transient
  var sparkContext: SparkContext = _
  @transient
  var sparkSession: SparkSession = _

  // Configuration for batch processing and thread management
  private val DEFAULT_BATCH_SIZE = 10000
  private val DEFAULT_DB_BATCH_SIZE = 10000
  private val DEFAULT_MAX_THREADS = 8
  private val DEFAULT_DB_CONNECTION_TIMEOUT = 30000
  private val DEFAULT_RETRY_ATTEMPTS = 3

  // Thread pool for database operations
  @transient
  private var dbThreadPool: ThreadPoolExecutor = _
  @transient
  private implicit var dbExecutionContext: ExecutionContext = _

//  @transient
//  private val dorisConnector: DorisConnector = new DorisConnector(AppConfig.getProperties("doris.datasource.url"),
//    AppConfig.getProperties("doris.datasource.username"),
//    AppConfig.getProperties("doris.datasource.password"))


  /**
   * Process data in streaming batches with parallel execution and thread limiting
   * This method fetches data in batches and processes them in parallel until all data is consumed
   */
  def processDataInStreamingBatches(
    unification: Unification,
    fields: List[String],
    ex: String,
    contactMap: Map[String, String],
    fieldSelectMap: Map[String, FieldSuggestData],
    dataTypeMap: Map[String, String],
    batchSize: Int,
    dbBatchSize: Int,
    maxRetries: Int
  ): Unit = {
    logger.info("Starting streaming batch processing")

    var totalProcessed = 0
    var batchNumber = 0
    var hasMoreData = true

    // Initialize Kafka publisher
    val unifyDataKafkaPublisher: UnifyDataMessagePublisher = {
      val kafkaConfig = new KafkaConfiguration(
        AppConfig.getProperties("kafka.publisher.bootstrap-servers"),
        AppConfig.getProperties("kafka.publisher.unify-topic"),
        AppConfig.getProperties("kafka.publisher.username"),
        AppConfig.getProperties("kafka.publisher.password")
      )
      val properties = KafkaPublisherProperties.producerProperties(kafkaConfig)
      new UnifyDataMessagePublisher(kafkaConfig, properties)
    }

    // Process data in streaming batches until no more data
    while (hasMoreData) {
      batchNumber += 1
      logger.info(s"Processing batch #$batchNumber (offset: $totalProcessed, size: $batchSize)")

      try {
        // Fetch next batch of raw data
        val rawDataBatch = fetchRawDataBatch(
          unification.userIdSource,
          unification.sourceId,
          offset = totalProcessed,
          limit = batchSize
        )

        if (rawDataBatch.isEmpty) {
          hasMoreData = false
          logger.info("No more data to process")
        } else {
          logger.info(s"Fetched ${rawDataBatch.size} records in batch #$batchNumber")

          // Process this batch in parallel with thread limiting
          val batchFutures = processBatchInParallel(
            rawDataBatch = rawDataBatch,
            unification = unification,
            fields = fields,
            ex = ex,
            contactMap = contactMap,
            fieldSelectMap = fieldSelectMap,
            dataTypeMap = dataTypeMap,
            unifyDataKafkaPublisher = unifyDataKafkaPublisher,
            dbBatchSize = dbBatchSize,
            maxRetries = maxRetries
          )

          // Wait for batch processing to complete
          import scala.concurrent.duration._
          val timeout = 30.minutes

          try {
            scala.concurrent.Await.result(batchFutures, timeout)
            totalProcessed += rawDataBatch.size
            logger.info(s"Successfully processed batch #$batchNumber. Total processed: $totalProcessed")
          } catch {
            case e: Exception =>
              logger.error(s"Error processing batch #$batchNumber: ${e.getMessage}", e)
              // Continue with next batch instead of failing completely
              totalProcessed += rawDataBatch.size
          }

          // Check if we got less data than requested (indicates end of data)
          if (rawDataBatch.size < batchSize) {
            hasMoreData = false
            logger.info(s"Reached end of data (batch size: ${rawDataBatch.size} < requested: $batchSize)")
          }
        }

      } catch {
        case e: Exception =>
          logger.error(s"Error fetching batch #$batchNumber: ${e.getMessage}", e)
          hasMoreData = false // Stop processing on fetch error
      }
    }

    logger.info(s"Streaming batch processing completed. Total records processed: $totalProcessed in $batchNumber batches")
  }

  /**
   * Process a single batch of raw data in parallel with thread limiting
   */
  def processBatchInParallel(
    rawDataBatch: List[(String, Map[String, String])],
    unification: Unification,
    fields: List[String],
    ex: String,
    contactMap: Map[String, String],
    fieldSelectMap: Map[String, FieldSuggestData],
    dataTypeMap: Map[String, String],
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    dbBatchSize: Int,
    maxRetries: Int
  ): Future[Unit] = {

    Future {
      println(s"Processing batch with ${rawDataBatch.size} raw data records")

      // PHASE 2: Merge raw data records directly into overall profiles
      println("PHASE 2: Merging raw data records into overall profiles")

      var profileAppend: List[Map[String, List[Object]]] = List()
      var profileMerge: Map[String, Map[String, Set[Object]]] = Map()

      // Collect query conditions for batch processing
      val batchQueryConditions = ListBuffer[(String, Map[String, Object])]()
      val recordProfiles = scala.collection.mutable.Map[String, Map[String, List[Object]]]()

      // Process each raw data record directly
      rawDataBatch.foreach { rawData =>
        val recordId = rawData._1
        val fieldDataMap = rawData._2

        // Convert to schema format
        var rawdataSchema: Map[String, List[Object]] = Map()

        // Build query conditions for profile matching
        var queryConditions: Map[String, Object] = Map()

        fieldDataMap.foreach { case (fieldSource, value) =>
          if (unification.mappingConfirm.contains(fieldSource)) {
            val fieldName = unification.mappingConfirm(fieldSource).fieldSource
            val dataType = unification.mappingConfirm(fieldSource).dataType

            // Process value based on data type
            val processedValue: Object = value

            // Add to schema
            if (!rawdataSchema.contains(fieldName)) rawdataSchema = rawdataSchema + (fieldName -> List())
            rawdataSchema = rawdataSchema + (fieldName -> (rawdataSchema(fieldName) :+ processedValue).distinct)

            // Add to query conditions if it's a field used for matching
            if (isKeyInFields(fieldSource, fields)) {
              queryConditions = queryConditions + (fieldName -> processedValue)
            }
          }
        }

        // Store the profile data for this record
        recordProfiles(recordId) = rawdataSchema

        // If we have query conditions, add to batch
        if (queryConditions.nonEmpty) {
          batchQueryConditions += ((recordId, queryConditions))
        } else if (rawdataSchema.nonEmpty) {
          // No query conditions, append directly
          profileAppend = profileAppend :+ rawdataSchema
          logger.debug(s"No query conditions available for record $recordId, will append as new profile")
        }
      }

      // Second pass: batch process all query conditions
      if (batchQueryConditions.nonEmpty) {
        try {
          logger.info(s"Searching for matching profiles in batch for ${batchQueryConditions.size} records")
          val batchResults = findMatchingProfilesBatch(fields, ex, batchQueryConditions.toList, unification.userIdProfileManagement)

          // Process batch results
          batchQueryConditions.foreach { case (recordId, _) =>
            val rawdataSchema = recordProfiles(recordId)

            if (!batchResults.contains(recordId) || batchResults(recordId).isEmpty) {
              // No matching profile found, append new profile
              profileAppend = profileAppend :+ rawdataSchema
              logger.debug(s"No matching profile found for record $recordId, will append as new profile")
            } else {
              // Matching profile found, prepare for merge
              val matchingProfiles = batchResults(recordId)
              val profileId = matchingProfiles.head._1
              val profileData = matchingProfiles.head._2

              logger.debug(s"Found matching profile $profileId for record $recordId with ${profileData.size} fields")

              if (!profileMerge.contains(profileId)) profileMerge = profileMerge + (profileId -> Map())

              var finalRawdata = profileMerge(profileId)

              // Convert profileData from Map[String, String] to the format needed for merging
              val convertedProfileData = profileData.map { case (field, value) =>
                field -> List[Object](value)
              }

              // Merge the converted profile data
              convertedProfileData.foreach(entry => {
                if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
              })

              // Merge the raw data schema
              rawdataSchema.foreach(entry => {
                if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
              })

              profileMerge = profileMerge + (profileId -> finalRawdata)
              logger.debug(s"Merged raw data from record $recordId into existing profile $profileId")
            }
          }
        } catch {
          case e: Exception =>
            logger.error(s"Error when searching for matching profiles in batch: ${e.getMessage}")
            e.printStackTrace()

            // Fallback to individual processing if batch processing fails
            logger.info("Falling back to individual profile processing")
            batchQueryConditions.foreach { case (recordId, queryConditions) =>
              try {
                val matchingProfiles = findMatchingProfiles(fields, ex, queryConditions, unification.userIdProfileManagement)
                val rawdataSchema = recordProfiles(recordId)

                if (matchingProfiles.isEmpty) {
                  // No matching profile found, append new profile
                  profileAppend = profileAppend :+ rawdataSchema
                  logger.debug(s"No matching profile found for record $recordId, will append as new profile")
                } else {
                  // Matching profile found, prepare for merge
                  val profileId = matchingProfiles.head._1
                  val profileData = matchingProfiles.head._2

                  logger.debug(s"Found matching profile $profileId for record $recordId with ${profileData.size} fields")

                  if (!profileMerge.contains(profileId)) profileMerge = profileMerge + (profileId -> Map())

                  var finalRawdata = profileMerge(profileId)

                  // Convert profileData from Map[String, String] to the format needed for merging
                  val convertedProfileData = profileData.map { case (field, value) =>
                    field -> List[Object](value)
                  }.toMap

                  // Merge the converted profile data
                  convertedProfileData.foreach(entry => {
                    if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                    finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
                  })

                  // Merge the raw data schema
                  rawdataSchema.foreach(entry => {
                    if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                    finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
                  })

                  profileMerge = profileMerge + (profileId -> finalRawdata)
                  logger.debug(s"Merged raw data from record $recordId into existing profile $profileId")
                }
              } catch {
                case e: Exception =>
                  logger.error(s"Error when searching for matching profiles for record $recordId: ${e.getMessage}")
                  e.printStackTrace()
                  // Add to append list as fallback
                  profileAppend = profileAppend :+ recordProfiles(recordId)
              }
            }
        }
      }

      // Process append with enhanced error handling and batching
      if (profileAppend.nonEmpty) {
        logger.info(s"Appending ${profileAppend.size} new profiles")
        withRetry {
          appendProfileEnhanced(unification.userIdProfileManagement, dataTypeMap, profileAppend, unification.sourceId, unifyDataKafkaPublisher, dbBatchSize)
        } match {
          case Success(_) => logger.info(s"Successfully appended ${profileAppend.size} profiles")
          case Failure(e) => logger.error(s"Failed to append profiles after retries: ${e.getMessage}", e)
        }
      }

      // Process merge with enhanced error handling and batching
      if (profileMerge.nonEmpty) {
        logger.info(s"Merging ${profileMerge.size} existing profiles")
        withRetry {
          mergeProfileEnhanced(unification.userIdProfileManagement, dataTypeMap, unification.sourceId, profileMerge, unifyDataKafkaPublisher, dbBatchSize)
        } match {
          case Success(_) => logger.info(s"Successfully merged ${profileMerge.size} profiles")
          case Failure(e) => logger.error(s"Failed to merge profiles after retries: ${e.getMessage}", e)
        }
      }

    }(dbExecutionContext)
  }

  /**
   * Initialize thread pool for database operations
   */
  private def initializeThreadPool(maxThreads: Int = DEFAULT_MAX_THREADS): Unit = {
    if (dbThreadPool == null || dbThreadPool.isShutdown) {
      dbThreadPool = Executors.newFixedThreadPool(maxThreads).asInstanceOf[ThreadPoolExecutor]
      dbExecutionContext = ExecutionContext.fromExecutor(dbThreadPool)
      logger.info(s"Initialized database thread pool with $maxThreads threads")
    }
  }

  /**
   * Shutdown thread pool
   */
  private def shutdownThreadPool(): Unit = {
    if (dbThreadPool != null && !dbThreadPool.isShutdown) {
      dbThreadPool.shutdown()
      try {
        if (!dbThreadPool.awaitTermination(60, TimeUnit.SECONDS)) {
          dbThreadPool.shutdownNow()
        }
      } catch {
        case _: InterruptedException =>
          dbThreadPool.shutdownNow()
      }
      logger.info("Database thread pool shutdown completed")
    }
  }

  /**
   * Get configuration value with default fallback
   */
  private def getConfigInt(key: String, default: Int): Int = {
    Try(AppConfig.getProperties(key).toInt).getOrElse(default)
  }

  /**
   * Execute operation with retry logic
   */
  private def withRetry[T](operation: => T, maxAttempts: Int = DEFAULT_RETRY_ATTEMPTS): Try[T] = {
    def attempt(attemptsLeft: Int): Try[T] = {
      Try(operation) match {
        case Success(result) => Success(result)
        case Failure(exception) if attemptsLeft > 1 =>
          logger.warn(s"Operation failed, retrying. Attempts left: ${attemptsLeft - 1}", exception)
          Thread.sleep(1000) // Wait 1 second before retry
          attempt(attemptsLeft - 1)
        case Failure(exception) => Failure(exception)
      }
    }
    attempt(maxAttempts)
  }

  def init(args: Array[String]): Unit = {
      val test: Boolean = if (args.length > 0) args(0).toBoolean else true
      val unificationId: String = if (args.length > 1) args(1) else ""

      if (Strings.isNullOrEmpty(unificationId)) {
        logger.error("unificationId input not found")
        sys.exit(-1)
      }

      sparkConf = new SparkConf()
      sparkConf.set("doris.fenodes", AppConfig.getProperties("doris.fenodes"))
      sparkConf.set("doris.table.identifier", AppConfig.getProperties("doris.database") + "." + AppConfig.getProperties("doris.table"))
      sparkConf.set("doris.user", AppConfig.getProperties("doris.user"))
      sparkConf.set("doris.password", AppConfig.getProperties("doris.password"))

      if (test) {
        sparkConf.setAppName("Unification Source Normal")
        sparkConf.setMaster("local[*]")
        println("Run mode test!")
      } else {
        sparkConf.setMaster("yarn")
        sparkConf.set("spark.yarn.queue", AppConfig.getProperties("spark.yarn.queue"))
        sparkConf.set("spark.sql.broadcastTimeout", "-1")
      }

      sparkContext = new SparkContext(sparkConf)
      sparkContext.setLogLevel("WARN")

      sparkSession = SparkSession.builder()
        .config(sparkContext.getConf)
        .getOrCreate()

      // initialize sqlContext
      sqlContext = sparkSession.sqlContext
      sparkConf.set("unification_id", unificationId)

      // Initialize thread pool for database operations
      val maxThreads = getConfigInt("unification.max.threads", DEFAULT_MAX_THREADS)
      initializeThreadPool(maxThreads)
  }

  def run(): Unit = {
    logger.info("=============================================")
    logger.info("Start application!")
    val startTime = System.currentTimeMillis()
    val unificationId: String = sparkConf.get("unification_id")

    logger.info("=============== DEBUG ==============================")
    logger.info(s"unification id: ${unificationId}")

    // Update source's unify status: 1-unifying, 2-done, -1-deleted
    // Get detail source unification's information
    val unification: Unification = APIHelper.apply().updateUnification(unificationId, 1)
    // Get source's mapping
    val sourceMapping: Map[String, SourceMapping] = APIHelper.apply().getSourceMapping(unification.sourceId)
    val rule: JSONObject = new JSONObject(unification.ruleUnification)
    // Logic: and, or,...
    val ex: String = rule.getString("ex")
    // fields: list field trong logic unify
    var fields: List[String] = List()
    for (i <- 0 until rule.getJSONArray("field").length()) fields = fields :+ rule.getJSONArray("field").get(i).toString

    // Fields contain contact information
    // contactMap bao gồm thông tin email và phone (thêm trường emails và phones đối với bizfly crm)
    var contactMap: Map[String, String] = Map()
    // Fields contain abbreviations that suggest information such as Mr, Ms, HN, HaNoi, Doanh Nghiep, Cty,...
    var fieldSelectMap: Map[String, FieldSuggestData] = Map()
    unification.mappingConfirm.foreach(mappingConfirm => {
      if (mappingConfirm._2.demographic.equals("phone")) contactMap = contactMap + (mappingConfirm._1 -> "phone")
      if (mappingConfirm._2.demographic.equals("email")) contactMap = contactMap + (mappingConfirm._1 -> "email")
      if (mappingConfirm._2.fieldSource.equals("phones")) contactMap = contactMap + (mappingConfirm._1 -> "phones")
      if (mappingConfirm._2.fieldSource.equals("emails")) contactMap = contactMap + (mappingConfirm._1 -> "emails")
      if (mappingConfirm._2.fieldDetect != null) {
        fieldSelectMap = fieldSelectMap + (mappingConfirm._1 -> APIHelper.apply().getDataDetectByFieldSource(mappingConfirm._1, mappingConfirm._2.fieldDetect))
      }
    })

    // Get configuration for batch processing
    val batchSize = getConfigInt("unification.batch.size", DEFAULT_BATCH_SIZE)
    val dbBatchSize = getConfigInt("unification.db.batch.size", DEFAULT_DB_BATCH_SIZE)
    val maxRetries = getConfigInt("unification.max.retries", DEFAULT_RETRY_ATTEMPTS)

    logger.info(s"Configuration: batchSize=$batchSize, dbBatchSize=$dbBatchSize, maxRetries=$maxRetries")

    try {
      // Process data in streaming batches to control memory usage
      processDataInStreamingBatches(
        unification = unification,
        fields = fields,
        ex = ex,
        contactMap = contactMap,
        fieldSelectMap = fieldSelectMap,
        dataTypeMap = APIHelper.apply().getDataTypeProfileManagementV2(unification.userIdProfileManagement),
        batchSize = batchSize,
        dbBatchSize = dbBatchSize,
        maxRetries = maxRetries
      )



      logger.info(s"Process completed in ${System.currentTimeMillis() - startTime} ms")

      // Update unification status
      APIHelper.apply().unificationSuccess(unificationId)

    } catch {
      case e: Exception =>
        logger.error(s"Error during unification process: ${e.getMessage}", e)
        // Update unification status to failed
        try {
          APIHelper.apply().updateUnification(unificationId, -1)
        } catch {
          case statusUpdateError: Exception =>
            logger.error(s"Failed to update unification status: ${statusUpdateError.getMessage}", statusUpdateError)
        }
        throw e
    } finally {
      // Cleanup resources
      shutdownThreadPool()
    }
  }

  /**
   * Method 3: Use JDBC to execute a completely custom SQL query directly against Doris
   * This bypasses the Spark-Doris connector entirely and gives you full SQL flexibility
   */
  private def executeJdbcQuery(userIdSource: String, sourceId: String) = {
    // Extract Doris connection information
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Define your custom SQL query
    val customQuery = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.table")}
      LIMIT 100
    """

    logger.info(s"Executing JDBC query: $customQuery")

    // Use Spark's JDBC data source to execute the query
    sparkSession.read
      .format("jdbc")
      .option("url", jdbcUrl)
      .option("user", jdbcUser)
      .option("password", jdbcPassword)
      .option("driver", "com.mysql.jdbc.Driver")
      .option("dbtable", s"(${customQuery}) as custom_query")
      .load()
  }

  /**
   * Fetch raw data in batches with pagination support
   * @param userId User ID
   * @param sourceId Source ID
   * @param offset Starting offset for pagination
   * @param limit Maximum number of records to fetch
   * @return List of (recordId, data) tuples where data contains all fields from the record
   */
  def fetchRawDataBatch(userId: String, sourceId: String, offset: Int, limit: Int): List[(String, Map[String, String])] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.raw_data_table") + userId + "_" + sourceId}
      ORDER BY _row_key
      LIMIT $limit OFFSET $offset
    """

    logger.info(s"Fetching raw data batch with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val recordId = resultSet.getString("_row_key")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns except id, user_id, and source_id
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName == "field_data") {
              val columnValueJson = resultSet.getString(i)
              if (columnValueJson != null) {
                val columnValueObj = new JSONObject(columnValueJson)
                val keys = columnValueObj.keys()
                while (keys.hasNext) {
                  val key = keys.next()
                  val value = columnValueObj.getString(key)
                  dataMap += (key -> value)
                }
              }
            }
          }

          result += ((recordId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error fetching raw data batch: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Fetched ${result.size} raw data records in batch (offset: $offset, limit: $limit)")
    result.toList
  }

  /**
   * Fetch raw data directly from Doris (legacy method for backward compatibility)
   * @param userId User ID
   * @param sourceId Source ID
   * @return List of (recordId, data) tuples where data contains all fields from the record
   */
  def fetchRawDataDirectly(userId: String, sourceId: String): List[(String, Map[String, String])] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.raw_data_table") + userId + "_" + sourceId}
    """

    logger.info(s"Fetching raw data directly with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      // For demonstration, we'll use JDBC directly instead of Spark
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val recordId = resultSet.getString("_row_key")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns except id, user_id, and source_id
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName == "field_data") {
              val columnValueJson = resultSet.getString(i)
              if (columnValueJson != null) {
                val columnValueObj = new JSONObject(columnValueJson)
                val keys = columnValueObj.keys()
                while (keys.hasNext) {
                  val key = keys.next()
                  val value = columnValueObj.getString(key)
                  dataMap += (key -> value)
                }
              }
            }
          }

          result += ((recordId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error fetching raw data: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Fetched ${result.size} raw data records")
    result.toList
  }

  /**
   * Get raw data from Doris for a list of record IDs (kept for backward compatibility)
   * @param userId User ID
   * @param sourceId Source ID
   * @param recordIds List of record IDs
   * @return List of (recordId, data) tuples where data contains all fields from the record
   */
  def getRawDataFromDoris(userId: String, sourceId: String, recordIds: List[String]): List[(String, Map[String, String])] = {
    if (recordIds.isEmpty) return List.empty[(String, Map[String, String])]

    // For better performance, we could use the fetchRawDataDirectly method and filter the results
    // However, for large datasets, it's more efficient to query only the specific records we need
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Create a comma-separated list of record IDs for the IN clause
    val recordIdsStr = recordIds.map(id => s"'$id'").mkString(", ")

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.raw_data_table") + userId + "_" + sourceId}
      WHERE user_id = '$userId' AND source_id = '$sourceId' AND id IN ($recordIdsStr)
    """

    logger.info(s"Getting raw data with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val recordId = resultSet.getString("id")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns except id, user_id, and source_id
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName != "id" && columnName != "user_id" && columnName != "source_id") {
              val columnValue = resultSet.getString(i)
              if (columnValue != null) {
                dataMap += (columnName -> columnValue)
              }
            }
          }

          result += ((recordId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error getting raw data: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Retrieved ${result.size} raw data records")
    result.toList
  }

  /**
   * Find matching profiles in Doris
   * @param fields Fields to match
   * @param ex Logic (and/or)
   * @param queryConditions Query conditions
   * @param userId User ID for profile management
   * @return List of (profileId, Map[String, String]) tuples
   */
  def findMatchingProfiles(fields: List[String], ex: String, queryConditions: Map[String, Object], userId: String): List[(String, Map[String, String])] = {
    if (queryConditions.isEmpty) return List.empty[(String, Map[String, String])]

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Check if field_data column exists
    var useFieldData = false
    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val metaDataStatement = connection.createStatement()
        val tableMetaQuery = s"DESCRIBE ${AppConfig.getProperties("doris.profile_person_table") + userId}"
        val metaDataResultSet = metaDataStatement.executeQuery(tableMetaQuery)

        var foundFieldData = false
        while (metaDataResultSet.next() && !foundFieldData) {
          val columnName = metaDataResultSet.getString("Field")
          if (columnName == "field_data") {
            useFieldData = true
            foundFieldData = true
          }
        }
        metaDataResultSet.close()
        metaDataStatement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error checking for field_data column: ${e.getMessage}")
        e.printStackTrace()
    }

    // Build WHERE clause based on query conditions and ex (AND/OR)
    val whereClause = if (useFieldData) {
      // For field_data column, use JSON_CONTAINS to query nested fields
      queryConditions.map { case (field, value) =>
        val valueStr = value match {
          case s: String => s"'${s.replace("'", "''").replace("\\", "\\\\")}'"
          case n: Number => s"'${n.toString}'"
          case _ => "'null'"
        }
        s"JSON_CONTAINS(JSON_EXTRACT(field_data, '$$.${field}[*].value'), $valueStr)"
      }.mkString(if (ex == "and") " AND " else " OR ")
    } else {
      // For normal table format, directly compare columns
      queryConditions.map { case (field, value) =>
        val valueStr = value match {
          case s: String => s"'$s'"
          case n: Number => n.toString
          case _ => "NULL"
        }
        s"$field = $valueStr"
      }.mkString(if (ex == "and") " AND " else " OR ")
    }

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
      WHERE $whereClause
      LIMIT 1
    """

    logger.info(s"Finding matching profiles with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val profileId = resultSet.getString("profile_id")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName != "profile_id") {
              val columnValue = resultSet.getString(i)
              if (columnValue != null) {
                dataMap += (columnName -> columnValue)
              }
            }
          }

          // If using field_data, extract fields from JSON
          if (useFieldData) {
            val fieldDataStr = resultSet.getString("field_data")
            if (fieldDataStr != null && fieldDataStr.nonEmpty) {
              try {
                val fieldDataJson = new JSONObject(fieldDataStr)
                val keys = fieldDataJson.keys()
                while (keys.hasNext()) {
                  val key = keys.next()
                  val valuesArray = fieldDataJson.getJSONArray(key)
                  if (valuesArray.length() > 0) {
                    // Use the first value's "value" field for simplicity
                    val valueObj = valuesArray.getJSONObject(0)
                    dataMap += (key -> valueObj.getString("value"))
                  }
                }
              } catch {
                case e: Exception =>
                  logger.error(s"Error parsing field_data JSON: ${e.getMessage}")
              }
            }
          }

          result += ((profileId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error finding matching profiles: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Found ${result.size} matching profiles")
    result.toList
  }

  def isTableEmpty(userId: String, tableNamePrefix: String): Boolean = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val query = s"SELECT COUNT(*) FROM ${AppConfig.getProperties(tableNamePrefix + userId)}"
        val resultSet = statement.executeQuery(query)

        if (resultSet.next()) {
          val count = resultSet.getInt(1)
          count == 0
        } else {
          true
        }
      } finally {
        connection.close()
      }
    }
    }

  /**
   * Find matching profiles in Doris in batch
   * @param fields Fields to match
   * @param ex Logic (and/or)
   * @param batchQueryConditions List of (groupId, queryConditions) tuples
   * @param userId User ID for profile management
   * @param batchSize Maximum number of conditions to include in a single query
   * @return Map of groupId to List of (profileId, Map[String, String]) tuples
   */
  def findMatchingProfilesBatch(
    fields: List[String],
    ex: String,
    batchQueryConditions: List[(String, Map[String, Object])],
    userId: String,
    batchSize: Int = 50
  ): Map[String, List[(String, Map[String, String])]] = {
    if (batchQueryConditions.isEmpty) return Map.empty[String, List[(String, Map[String, String])]]

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Process each group separately and combine results
    // This is more reliable than using CASE statements to track conditions
    val allResults = scala.collection.mutable.Map[String, List[(String, Map[String, String])]]()

    // Process in batches to avoid too many database connections
    batchQueryConditions.grouped(batchSize).foreach { batchConditions =>
      logger.info(s"Processing batch of ${batchConditions.size} conditions")

      // Process each group in the batch
      batchConditions.foreach { case (groupId, conditions) =>
        if (conditions.isEmpty) {
          logger.info(s"Skipping group $groupId with empty conditions")
          allResults(groupId) = List.empty[(String, Map[String, String])]
        } else {
          // Build lateral view for each needed field inside field_data
          val lateralViews = fields.map(field => s"lateral view explode_json_array_json(field_data['$field']) tmp as ${field.replace(" ", "_")}_entry")
            .mkString("\n")

          // Build WHERE clause for this group's conditions
          val whereClause = conditions.map { case (field, value) =>
            val valueStr = value match {
              case s: String => s"'${s.replace("'", "''").replace("\\", "\\\\")}'"
              case n: Number => s"${n.toString}"
              case _ => "'null'"
            }
            s"""${field.replace(" ", "_")}_entry['value'] = '${valueStr.replace("'", "\"")}' """
          }.mkString(if (ex == "and") " AND " else " OR ")

          val query = s"""
            SELECT *
            FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
            $lateralViews
            WHERE ($whereClause) and is_deleted = 0
          """

          logger.info(s"Finding matching profiles for group $groupId with query: $query")
//          println(s"Finding matching profiles for group $groupId with query: $query")

          val groupResults = ListBuffer[(String, Map[String, String])]()

          try {
            Class.forName("com.mysql.jdbc.Driver")
            val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
            try {
              val statement = connection.createStatement()
              val resultSet = statement.executeQuery(query)
              val metaData = resultSet.getMetaData
              val columnCount = metaData.getColumnCount

              while (resultSet.next()) {
                val profileId = resultSet.getString("profile_id")
                val dataMap = Map.newBuilder[String, String]

                // Extract all columns except profile_id
                for (i <- 1 to columnCount) {
                  val columnName = metaData.getColumnName(i)
                  if (columnName != "profile_id" && columnName != "field_data") {
                    val columnValue = resultSet.getString(i)
                    if (columnValue != null) {
                      dataMap += (columnName -> columnValue)
                    }
                  }
                }

                // If using field_data, extract fields from JSON
                val fieldDataStr = resultSet.getString("field_data")
                if (fieldDataStr != null && fieldDataStr.nonEmpty && fieldDataStr.length() > 2) {
                  try {
                    val fieldDataJson = new JSONObject(fieldDataStr)
                    val keys = fieldDataJson.keys()
                    while (keys.hasNext()) {
                      val key = keys.next()
                      val valuesArray = fieldDataJson.getJSONArray(key)
                      if (valuesArray.length() > 0) {
                        // Use the first value's "value" field for simplicity
                        val valueObj = valuesArray.getJSONObject(0)
                        dataMap += (key -> valueObj.getString("value"))
                      }
                    }
                  } catch {
                    case e: Exception =>
                      logger.error(s"Error parsing field_data JSON: ${e.getMessage} - $fieldDataStr")
                      println(s"Error resultSet.getString: ${resultSet.getString("profile_id")}")
                      println(s"Error parsing query JSON: ${e.getMessage} - $query")
                      System.exit(1)
                  }
                }

                groupResults += ((profileId, dataMap.result()))
              }

              resultSet.close()
              statement.close()
            } finally {
              connection.close()
            }
          } catch {
            case e: Exception =>
              logger.error(s"Error finding matching profiles for group $groupId: ${e.getMessage}")
              e.printStackTrace()
          }

          // Store results for this group
          allResults(groupId) = groupResults.toList
          logger.info(s"Found ${groupResults.size} matching profiles for group $groupId")
        }
      }
    }

    // Convert mutable map to immutable map for return
    val finalResults = allResults.toMap

    logger.info(s"Found matching profiles for ${finalResults.size} groups out of ${batchQueryConditions.size} total groups")
    finalResults
  }

  /**
   * Append new profiles to Doris
   * @param userId User ID
   * @param dataTypeMap Data type map
   * @param profileData Profile data
   * @param sourceId Source ID
   * @param unifyDataKafkaPublisher Kafka publisher
   * @param batchSize Maximum number of profiles to insert in a single query
   */
  def appendProfile(
    userId: String,
    dataTypeMap: Map[String, String],
    profileData: List[Map[String, List[Object]]],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = 100
  ): Unit = {
    if (profileData.isEmpty) return

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Process in batches to avoid overly large queries
        profileData.grouped(batchSize).foreach { batchData =>
          // Prepare profile IDs and Kafka messages for this batch
          val profileIds = batchData.map(_ => java.util.UUID.randomUUID().toString)
          val timestamp = System.currentTimeMillis()

          // Build the batch INSERT statement
          val insertPrefix = s"""
            INSERT INTO ${AppConfig.getProperties("doris.profile_person_table") + userId}
            (profile_id, last_update, sources, field_data)
            VALUES
          """

          val valuesList = batchData.zip(profileIds).map { case (data, profileId) =>
            // Convert profile data to JSON structure
            val fieldDataJson = new JSONObject()

            data.foreach { case (field, values) =>
              if (values.nonEmpty) {
                // Store values as an array of objects with source information in the field_data
                val valuesArray = new JSONArray()
                values.foreach { value =>
                  // Create an object for each value with the value and its source
                  val valueObj = new JSONObject()
                  valueObj.put("value", value.toString)

                  // Add source information as a concatenated string
                  valueObj.put("sources", sourceId)

                  valuesArray.put(valueObj)
                }
                fieldDataJson.put(field, valuesArray)
              }
            }

            // Create the VALUES tuple
            val jsonString = escapeJsonForSql(fieldDataJson.toString)
            s"""('$profileId', $timestamp, '["$sourceId"]', $jsonString)"""
          }

          // Execute the batch INSERT
          val sql = insertPrefix + valuesList.mkString(",\n")

          logger.info(s"Appending ${batchData.size} profiles with batch query")
          val statement = connection.createStatement()
          statement.execute(sql)
          statement.close()

          // Publish to Kafka
          batchData.zip(profileIds).foreach { case (_, profileId) =>
            val kafkaInfo = new JSONObject()
            kafkaInfo.put("profile_id", profileId)
            kafkaInfo.put("is_delete", 0)
            kafkaInfo.put("sources", new JSONArray().put(sourceId))
            kafkaInfo.put("user_id", userId)
            unifyDataKafkaPublisher.publish(kafkaInfo.toString())
          }
        }
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error appending profiles: ${e.getMessage}")
        e.printStackTrace()
    }
  }

  /**
   * Merge profiles in Doris
   * @param userId User ID
   * @param dataTypeMap Data type map
   * @param sourceId Source ID
   * @param profileMerge Profile merge data
   * @param unifyDataKafkaPublisher Kafka publisher
   * @param batchSize Maximum number of profiles to fetch or update in a single query
   */
  def mergeProfile(
    userId: String,
    dataTypeMap: Map[String, String],
    sourceId: String,
    profileMerge: Map[String, Map[String, Set[Object]]],
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = 1000
  ): Unit = {
    if (profileMerge.isEmpty) return

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Process in batches to avoid overly large queries
        profileMerge.keys.grouped(batchSize).foreach { batchProfileIds =>
          // Fetch all profiles in this batch with a single query
          val profileIdList = batchProfileIds.map(id => s"'$id'").mkString(", ")
          val query = s"""
            SELECT profile_id, sources, field_data
            FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
            WHERE profile_id IN ($profileIdList)
          """

          logger.info(s"Fetching ${batchProfileIds.size} profiles with batch query")

          val statement = connection.createStatement()
          val resultSet = statement.executeQuery(query)

          // Process each profile and prepare updates
          val updateStatements = ListBuffer[String]()
          val kafkaMessages = ListBuffer[(String, List[String])]()

          // Map to store profile data by profile ID
          val profileDataMap = scala.collection.mutable.Map[String, (String, JSONObject)]()

          // Extract profile data
          while (resultSet.next()) {
            val profileId = resultSet.getString("profile_id")
            val sourcesStr = resultSet.getString("sources")
            val fieldDataStr = resultSet.getString("field_data")

            // Parse field_data JSON
            val fieldDataJson = if (fieldDataStr != null && fieldDataStr.nonEmpty) {
              new JSONObject(fieldDataStr)
            } else {
              new JSONObject()
            }

            profileDataMap += (profileId -> (sourcesStr, fieldDataJson))
          }

          resultSet.close()

          // Process each profile in the batch
          batchProfileIds.foreach { profileId =>
            if (profileDataMap.contains(profileId)) {
              val (currentSourcesStr, fieldDataJson) = profileDataMap(profileId)

              // Update sources
              val currentSources = if (currentSourcesStr != null && currentSourcesStr.nonEmpty) {
                val sourcesArray = new JSONArray(currentSourcesStr)
                val sourcesList = ListBuffer[String]()
                for (i <- 0 until sourcesArray.length()) {
                  sourcesList += sourcesArray.getString(i)
                }
                sourcesList.toList
              } else {
                List.empty[String]
              }

              val updatedSources = if (currentSources.contains(sourceId)) currentSources else currentSources :+ sourceId
//              val sourcesJson = new JSONArray(updatedSources.asJava).toString()

              // Merge new field data
              profileMerge(profileId).foreach { case (field, values) =>
                if (!field.equals("field_data")) return
                if (values.nonEmpty) {
                  // Get existing values for this field
                  val existingValuesMap = if (fieldDataJson.has(field)) {
                    val existingArray = fieldDataJson.getJSONArray(field)
                    val valuesMap = scala.collection.mutable.Map[String, Set[String]]()

                    for (i <- 0 until existingArray.length()) {
                      val valueObj = existingArray.getJSONObject(i)
                      val value = valueObj.getString("value")

                      // Extract sources for this value (stored as concatenated string)
                      val sourcesStr = valueObj.getString("sources")
                      val sources = if (sourcesStr.nonEmpty) {
                        sourcesStr.split("\\|").toSet
                      } else {
                        Set.empty[String]
                      }

                      valuesMap += (value -> sources)
                    }

                    valuesMap
                  } else {
                    scala.collection.mutable.Map.empty[String, Set[String]]
                  }

                  // Add new values with source information
                  values.foreach { value =>
                    val valueStr = value.toString
                    val updatedSources = if (existingValuesMap.contains(valueStr)) {
                      existingValuesMap(valueStr) + sourceId
                    } else {
                      Set(sourceId)
                    }
                    existingValuesMap(valueStr) = updatedSources
                  }

                  // Update the field in JSON
                  val newValuesArray = new JSONArray()
                  existingValuesMap.foreach { case (value, sources) =>
                    val valueObj = new JSONObject()
                    valueObj.put("value", value)

                    // Join sources with pipe delimiter
                    val sourcesStr = sources.mkString("|")
                    valueObj.put("sources", sourcesStr)

                    newValuesArray.put(valueObj)
                  }
                  fieldDataJson.put(field, newValuesArray)
                }
              }

              // Create UPDATE statement
              val updateSql = s"""
                UPDATE ${AppConfig.getProperties("doris.profile_person_table") + userId}
                SET
                  field_data = '${fieldDataJson.toString()}',
                  sources = '[${updatedSources.mkString(",")}]',
                  last_update = ${System.currentTimeMillis()}
                WHERE profile_id = '$profileId'
              """
              println("updateSql: " + updateSql)

              updateStatements += updateSql
              kafkaMessages += ((profileId, updatedSources))
            }
          }

          // Execute all updates
          if (updateStatements.nonEmpty) {
            logger.info(s"Executing ${updateStatements.size} profile updates")

            // Execute each update statement
            updateStatements.foreach { sql =>
              statement.execute(sql)
            }

            // Publish to Kafka
            kafkaMessages.foreach { case (profileId, updatedSources) =>
              val kafkaInfo = new JSONObject()
              kafkaInfo.put("profile_id", profileId)
              kafkaInfo.put("is_delete", 0)
              kafkaInfo.put("sources", s"[${updatedSources.mkString(",")}]")
              kafkaInfo.put("user_id", userId)
              unifyDataKafkaPublisher.publish(kafkaInfo.toString())
            }
          }

          statement.close()
        }
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error merging profiles: ${e.getMessage}")
        e.printStackTrace()
    }
  }

  /**
   * Check if a key is in the fields list
   * @param key Key to check
   * @param fields Fields list
   * @return True if the key is in the fields list
   */
  private def isKeyInFields(key: String, fields: List[String]): Boolean = {
    for (field <- fields) if (isKeyInField(key, field)) return true
    false
  }

  /**
   * Check if a key is in a field
   * @param key Key to check
   * @param field Field to check
   * @return True if the key is in the field
   */
  private def isKeyInField(key: String, field: String): Boolean = {
    if (field.equals(key)) return true
    if (field.contains(".")) {
      val fieldPath = field.split('.')
      if (fieldPath.contains(key)) return true
    }
    false
  }

  /**
   * Enhanced append profile method with better batch processing and thread management
   */
  def appendProfileEnhanced(
    userId: String,
    dataTypeMap: Map[String, String],
    profileData: List[Map[String, List[Object]]],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = DEFAULT_DB_BATCH_SIZE
  ): Unit = {
    if (profileData.isEmpty) return

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Process in smaller batches with parallel execution
    val futures = profileData.grouped(batchSize).map { batchData =>
      Future {
        withRetry {
          processAppendBatch(jdbcUrl, jdbcUser, jdbcPassword, userId, batchData, sourceId, unifyDataKafkaPublisher)
        }
      }(dbExecutionContext)
    }.toList

    // Wait for all batches to complete
    import scala.concurrent.duration._
    val timeout = 10.minutes

    try {
      futures.foreach { future =>
        scala.concurrent.Await.result(future, timeout) match {
          case Success(_) => // Success
          case Failure(e) => throw e
        }
      }
      logger.info(s"Successfully processed ${profileData.size} profiles in ${futures.size} batches")
    } catch {
      case e: Exception =>
        logger.error(s"Error in enhanced append profile: ${e.getMessage}", e)
        throw e
    }
  }

  /**
   * Process a single append batch
   */
  private def processAppendBatch(
    jdbcUrl: String,
    jdbcUser: String,
    jdbcPassword: String,
    userId: String,
    batchData: List[Map[String, List[Object]]],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher
  ): Unit = {
    Class.forName("com.mysql.jdbc.Driver")
    val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
    try {
      connection.setAutoCommit(false)

      // Prepare profile IDs and Kafka messages for this batch
      val profileIds = batchData.map(_ => java.util.UUID.randomUUID().toString)
      val timestamp = System.currentTimeMillis()

      // Build the batch INSERT statement
      val insertPrefix = s"""
        INSERT INTO ${AppConfig.getProperties("doris.profile_person_table") + userId}
        (profile_id, last_update, sources, field_data)
        VALUES
      """

      val valuesList = batchData.zip(profileIds).map { case (data, profileId) =>
        // Convert profile data to JSON structure
        val fieldDataJson = new JSONObject()

        data.foreach { case (field, values) =>
          if (values.nonEmpty) {
            // Store values as an array of objects with source information in the field_data
            val valuesArray = new JSONArray()
            values.foreach { value =>
              // Create an object for each value with the value and its source
              val valueObj = new JSONObject()
              valueObj.put("value", value.toString)

              // Add source information as a concatenated string
              valueObj.put("sources", sourceId)

              valuesArray.put(valueObj)
            }
            fieldDataJson.put(field, valuesArray)
          }
        }

        // Create the VALUES tuple
        val jsonString = escapeJsonForSql(fieldDataJson.toString)
        s"""('$profileId', $timestamp, '["$sourceId"]', $jsonString)"""
      }

      // Execute the batch INSERT
      val sql = insertPrefix + valuesList.mkString(",\n")

      logger.debug(s"Executing batch INSERT for ${batchData.size} profiles")
      val statement = connection.createStatement()
      statement.execute(sql)
      statement.close()

      connection.commit()

      // Publish to Kafka
      batchData.zip(profileIds).foreach { case (_, profileId) =>
        val kafkaInfo = new JSONObject()
        kafkaInfo.put("profile_id", profileId)
        kafkaInfo.put("is_delete", 0)
        kafkaInfo.put("sources", new JSONArray().put(sourceId))
        kafkaInfo.put("user_id", userId)
        unifyDataKafkaPublisher.publish(kafkaInfo.toString())
      }

      logger.debug(s"Successfully processed append batch of ${batchData.size} profiles")
    } catch {
      case e: Exception =>
        connection.rollback()
        throw e
    } finally {
      connection.close()
    }
  }

  /**
   * Enhanced merge profile method with better batch processing and thread management
   */
  def mergeProfileEnhanced(
    userId: String,
    dataTypeMap: Map[String, String],
    sourceId: String,
    profileMerge: Map[String, Map[String, Set[Object]]],
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = DEFAULT_DB_BATCH_SIZE
  ): Unit = {
    if (profileMerge.isEmpty) return

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Process in smaller batches with parallel execution
    val futures = profileMerge.keys.grouped(batchSize).map { batchProfileIds =>
      Future {
        withRetry {
          processMergeBatch(jdbcUrl, jdbcUser, jdbcPassword, userId, batchProfileIds.toList, profileMerge, sourceId, unifyDataKafkaPublisher)
        }
      }(dbExecutionContext)
    }.toList

    // Wait for all batches to complete
    import scala.concurrent.duration._
    val timeout = 10.minutes

    try {
      futures.foreach { future =>
        scala.concurrent.Await.result(future, timeout) match {
          case Success(_) => // Success
          case Failure(e) => throw e
        }
      }
      logger.info(s"Successfully processed ${profileMerge.size} profile merges in ${futures.size} batches")
    } catch {
      case e: Exception =>
        logger.error(s"Error in enhanced merge profile: ${e.getMessage}", e)
        throw e
    }
  }

  /**
   * Process a single merge batch
   */
  private def processMergeBatch(
    jdbcUrl: String,
    jdbcUser: String,
    jdbcPassword: String,
    userId: String,
    batchProfileIds: List[String],
    profileMerge: Map[String, Map[String, Set[Object]]],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher
  ): Unit = {
    Class.forName("com.mysql.jdbc.Driver")
    val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
    try {
      connection.setAutoCommit(false)

      // Fetch all profiles in this batch with a single query
      val profileIdList = batchProfileIds.map(id => s"'$id'").mkString(", ")
      val query = s"""
        SELECT profile_id, sources, field_data
        FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
        WHERE profile_id IN ($profileIdList)
      """

      logger.debug(s"Fetching ${batchProfileIds.size} profiles for merge")

      val statement = connection.createStatement()
      val resultSet = statement.executeQuery(query)

      // Process each profile and prepare updates
      val updateStatements = ListBuffer[String]()
      val kafkaMessages = ListBuffer[(String, List[String])]()

      // Map to store profile data by profile ID
      val profileDataMap = scala.collection.mutable.Map[String, (String, JSONObject)]()

      // Extract profile data
      while (resultSet.next()) {
        val profileId = resultSet.getString("profile_id")
        val sourcesStr = resultSet.getString("sources")
        val fieldDataStr = resultSet.getString("field_data")

        // Parse field_data JSON
        val fieldDataJson = if (fieldDataStr != null && fieldDataStr.nonEmpty) {
          new JSONObject(fieldDataStr)
        } else {
          new JSONObject()
        }

        profileDataMap += (profileId -> (sourcesStr, fieldDataJson))
      }

      resultSet.close()

      // Process each profile in the batch
      batchProfileIds.foreach { profileId =>
        if (profileDataMap.contains(profileId)) {
          val (currentSourcesStr, fieldDataJson) = profileDataMap(profileId)

          // Update sources
          val currentSources = if (currentSourcesStr != null && currentSourcesStr.nonEmpty) {
            val sourcesArray = new JSONArray(currentSourcesStr)
            val sourcesList = ListBuffer[String]()
            for (i <- 0 until sourcesArray.length()) {
              sourcesList += sourcesArray.getString(i)
            }
            sourcesList.toList
          } else {
            List.empty[String]
          }

          val updatedSources = if (currentSources.contains(sourceId)) currentSources else currentSources :+ sourceId

          // Merge new field data
          profileMerge(profileId).foreach { case (field, values) =>
            if (!field.equals("field_data") && values.nonEmpty) {
              // Get existing values for this field
              val existingValuesMap = if (fieldDataJson.has(field)) {
                val existingArray = fieldDataJson.getJSONArray(field)
                val valuesMap = scala.collection.mutable.Map[String, Set[String]]()

                for (i <- 0 until existingArray.length()) {
                  val valueObj = existingArray.getJSONObject(i)
                  val value = valueObj.getString("value")

                  // Extract sources for this value (stored as concatenated string)
                  val sourcesStr = valueObj.getString("sources")
                  val sources = if (sourcesStr.nonEmpty) {
                    sourcesStr.split("\\|").toSet
                  } else {
                    Set.empty[String]
                  }

                  valuesMap += (value -> sources)
                }

                valuesMap
              } else {
                scala.collection.mutable.Map.empty[String, Set[String]]
              }

              // Add new values with source information
              values.foreach { value =>
                val valueStr = value.toString
                val updatedSources = if (existingValuesMap.contains(valueStr)) {
                  existingValuesMap(valueStr) + sourceId
                } else {
                  Set(sourceId)
                }
                existingValuesMap(valueStr) = updatedSources
              }

              // Update the field in JSON
              val newValuesArray = new JSONArray()
              existingValuesMap.foreach { case (value, sources) =>
                val valueObj = new JSONObject()
                valueObj.put("value", value)

                // Join sources with pipe delimiter
                val sourcesStr = sources.mkString("|")
                valueObj.put("sources", sourcesStr)

                newValuesArray.put(valueObj)
              }
              fieldDataJson.put(field, newValuesArray)
            }
          }

          // Create UPDATE statement
          val updateSql = s"""
            UPDATE ${AppConfig.getProperties("doris.profile_person_table") + userId}
            SET
              field_data = '${fieldDataJson.toString()}',
              sources = '[${updatedSources.mkString(",")}]',
              last_update = ${System.currentTimeMillis()}
            WHERE profile_id = '$profileId'
          """

          updateStatements += updateSql
          kafkaMessages += ((profileId, updatedSources))
        }
      }

      // Execute all updates
      if (updateStatements.nonEmpty) {
        logger.debug(s"Executing ${updateStatements.size} profile updates")

        // Execute each update statement
        updateStatements.foreach { sql =>
          statement.execute(sql)
        }

        connection.commit()

        // Publish to Kafka
        kafkaMessages.foreach { case (profileId, updatedSources) =>
          val kafkaInfo = new JSONObject()
          kafkaInfo.put("profile_id", profileId)
          kafkaInfo.put("is_delete", 0)
          kafkaInfo.put("sources", s"[${updatedSources.mkString(",")}]")
          kafkaInfo.put("user_id", userId)
          unifyDataKafkaPublisher.publish(kafkaInfo.toString())
        }

        logger.debug(s"Successfully processed merge batch of ${updateStatements.size} profiles")
      }

      statement.close()
    } catch {
      case e: Exception =>
        connection.rollback()
        throw e
    } finally {
      connection.close()
    }
  }

  /**
   * Properly escapes a JSON string for inclusion in a SQL statement
   * @param json The JSON string to escape
   * @return Properly escaped string for SQL
   */
  private def escapeJsonForSql(json: String): String = {
    // First escape single quotes for SQL by doubling them
    val sqlEscaped = json.replace("'", "''")
    // Then wrap in single quotes for the SQL string
    s"'$sqlEscaped'"
  }

  /**
   * Remove a specific source from a profile
   * @param userId User ID
   * @param profileId Profile ID
   * @param sourceId Source ID to remove
   * @param unifyDataKafkaPublisher Kafka publisher
   * @return True if successful, false otherwise
   */
  def removeSourceFromProfile(
    userId: String,
    profileId: String,
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher
  ): Boolean = {
    removeSourceFromProfiles(userId, List(profileId), sourceId, unifyDataKafkaPublisher).contains(profileId)
  }

  /**
   * Remove a specific source from multiple profiles
   * @param userId User ID
   * @param profileIds List of profile IDs
   * @param sourceId Source ID to remove
   * @param unifyDataKafkaPublisher Kafka publisher
   * @return Set of successfully processed profile IDs
   */
  def removeSourceFromProfiles(
    userId: String,
    profileIds: List[String],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = 100
  ): Set[String] = {
    if (profileIds.isEmpty) return Set.empty[String]

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val successfulProfiles = scala.collection.mutable.Set[String]()

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Process in batches to avoid overly large queries
        profileIds.grouped(batchSize).foreach { batchProfileIds =>
          // Fetch all profiles in this batch with a single query
          val profileIdList = batchProfileIds.map(id => s"'$id'").mkString(", ")
          val query = s"""
            SELECT profile_id, sources, field_data
            FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
            WHERE profile_id IN ($profileIdList)
          """

          logger.info(s"Fetching ${batchProfileIds.size} profiles to remove source $sourceId")

          val statement = connection.createStatement()
          val resultSet = statement.executeQuery(query)

          // Store profile data for processing
          val profileDataMap = scala.collection.mutable.Map[String, (List[String], JSONObject)]()

          // Extract profile data
          while (resultSet.next()) {
            val profileId = resultSet.getString("profile_id")
            val sourcesStr = resultSet.getString("sources")
            val fieldDataStr = resultSet.getString("field_data")

            // Parse sources JSON
            val currentSources = if (sourcesStr != null && sourcesStr.nonEmpty) {
              val sourcesArray = new JSONArray(sourcesStr)
              val sourcesList = ListBuffer[String]()
              for (i <- 0 until sourcesArray.length()) {
                sourcesList += sourcesArray.getString(i)
              }
              sourcesList.toList
            } else {
              List.empty[String]
            }

            // Parse field_data JSON
            val fieldDataJson = if (fieldDataStr != null && fieldDataStr.nonEmpty) {
              new JSONObject(fieldDataStr)
            } else {
              new JSONObject()
            }

            // Only process profiles that contain the source
            if (currentSources.contains(sourceId)) {
              profileDataMap += (profileId -> (currentSources, fieldDataJson))
            } else {
              logger.info(s"Source $sourceId not found in profile $profileId, skipping")
            }
          }

          resultSet.close()

          // Prepare statements for batch execution
          val deleteStatements = ListBuffer[String]()
          val updateStatements = ListBuffer[String]()
          val kafkaMessages = ListBuffer[(String, Boolean, List[String])]() // (profileId, isDelete, sources)

          // Process each profile
          profileDataMap.foreach { case (profileId, (currentSources, fieldDataJson)) =>
            // Remove the source from the sources list
            val updatedSources = currentSources.filter(_ != sourceId)

            // Remove the source from each field value
            val keys = fieldDataJson.keys()
            while (keys.hasNext()) {
              val field = keys.next()
              val valuesArray = fieldDataJson.getJSONArray(field)
              val updatedValuesArray = new JSONArray()

              // Process each value in the field
              for (i <- 0 until valuesArray.length()) {
                val valueObj = valuesArray.getJSONObject(i)
                val sourcesStr = valueObj.getString("sources")

                // Split the sources string and filter out the removed source
                val sources = sourcesStr.split("\\|")
                val updatedSources = sources.filter(_ != sourceId)

                // Only keep values that still have sources after removal
                if (updatedSources.nonEmpty) {
//                  valueObj.put("sources", updatedSources.mkString("|")
//                  updatedValuesArray.put(valueObj)
                }
              }

              // Update the field with the filtered values
              fieldDataJson.put(field, updatedValuesArray)
            }

            // If no sources remain, delete the profile
            if (updatedSources.isEmpty) {
              val deleteSql = s"""
                DELETE FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
                WHERE profile_id = '$profileId'
              """

              deleteStatements += deleteSql
              kafkaMessages += ((profileId, true, List.empty[String]))
              logger.info(s"Preparing to delete profile $profileId as it has no remaining sources")
            } else {
              // Update the profile with the modified data
              val updateSql = s"""
                UPDATE ${AppConfig.getProperties("doris.profile_person_table") + userId}
                SET
                  field_data = '${escapeJsonForSql(fieldDataJson.toString())}',
                  sources = '[${updatedSources.mkString(",")}]',
                  last_update = ${System.currentTimeMillis()}
                WHERE profile_id = '$profileId'
              """

              updateStatements += updateSql
              kafkaMessages += ((profileId, false, updatedSources))
              logger.info(s"Preparing to update profile $profileId after removing source $sourceId")
            }

            // Mark as successful
            successfulProfiles += profileId
          }

          // Execute all statements
          if (deleteStatements.nonEmpty || updateStatements.nonEmpty) {
            val batchStatement = connection.createStatement()

            // Execute delete statements
            deleteStatements.foreach { sql =>
              try {
                batchStatement.execute(sql)
              } catch {
                case e: Exception =>
                  logger.error(s"Error executing delete statement: ${e.getMessage}")
                  e.printStackTrace()
              }
            }

            // Execute update statements
            updateStatements.foreach { sql =>
              try {
                batchStatement.execute(sql)
              } catch {
                case e: Exception =>
                  logger.error(s"Error executing update statement: ${e.getMessage}")
                  e.printStackTrace()
              }
            }

            batchStatement.close()

            // Publish to Kafka
            kafkaMessages.foreach { case (profileId, isDelete, updatedSources) =>
              val kafkaInfo = new JSONObject()
              kafkaInfo.put("profile_id", profileId)
              kafkaInfo.put("is_delete", if (isDelete) 1 else 0)
              kafkaInfo.put("sources", if (isDelete) "[]" else s"[${updatedSources.mkString(",")}]")
              kafkaInfo.put("user_id", userId)
              unifyDataKafkaPublisher.publish(kafkaInfo.toString())
            }
          }
        }
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error removing source $sourceId from profiles: ${e.getMessage}")
        e.printStackTrace()
    }

    successfulProfiles.toSet
  }

  /**
   * Find all profiles that contain a specific source
   * @param userId User ID
   * @param sourceId Source ID to find
   * @param limit Maximum number of profiles to return (0 for no limit)
   * @return List of profile IDs
   */
  def findProfilesBySource(
    userId: String,
    sourceId: String,
    limit: Int = 0
  ): List[String] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val profileIds = ListBuffer[String]()

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Build query to find profiles containing the source
        val limitClause = if (limit > 0) s"LIMIT $limit" else ""
        val query = s"""
          SELECT profile_id
          FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
          WHERE JSON_CONTAINS(sources, '"$sourceId"')
          $limitClause
        """

        logger.info(s"Finding profiles containing source $sourceId")

        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)

        while (resultSet.next()) {
          profileIds += resultSet.getString("profile_id")
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error finding profiles by source $sourceId: ${e.getMessage}")
        e.printStackTrace()
    }

    profileIds.toList
  }

  /**
   * Get field values with their sources for a specific profile
   * @param userId User ID
   * @param profileId Profile ID
   * @param fieldName Field name (optional, if not provided returns all fields)
   * @return Map of field names to list of (value, sources) tuples
   */
  def getProfileFieldSources(
    userId: String,
    profileId: String,
    fieldName: Option[String] = None
  ): Map[String, List[(String, List[String])]] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    var result = Map.empty[String, List[(String, List[String])]]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Build query to get the profile
        val fieldSelector = fieldName.map(f => s"JSON_EXTRACT(field_data, '$$.${f}')").getOrElse("field_data")
        val query = s"""
          SELECT $fieldSelector as field_data
          FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
          WHERE profile_id = '$profileId'
        """

        logger.info(s"Getting field sources for profile $profileId${fieldName.map(f => s" field $f").getOrElse("")}")

        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)

        if (resultSet.next()) {
          val fieldDataStr = resultSet.getString("field_data")

          if (fieldDataStr != null && fieldDataStr.nonEmpty) {
            try {
              // Parse the field data JSON
              val fieldDataJson = if (fieldName.isDefined) {
                // If a specific field was requested, the result is already the field array
                val fieldArray = new JSONArray(fieldDataStr)
                val singleFieldJson = new JSONObject()
                singleFieldJson.put(fieldName.get, fieldArray)
                singleFieldJson
              } else {
                // Otherwise, it's the full field_data object
                new JSONObject(fieldDataStr)
              }

              // Extract field values and sources
              val keys = fieldDataJson.keys()
              val resultBuilder = Map.newBuilder[String, List[(String, List[String])]]

              while (keys.hasNext()) {
                val field = keys.next()
                val valuesArray = fieldDataJson.getJSONArray(field)
                val fieldValues = ListBuffer[(String, List[String])]()

                for (i <- 0 until valuesArray.length()) {
                  val valueObj = valuesArray.getJSONObject(i)
                  val value = valueObj.getString("value")

                  // Extract sources from concatenated string
                  val sourcesStr = valueObj.getString("sources")
                  val sources = if (sourcesStr.nonEmpty) {
                    sourcesStr.split("\\|").toList
                  } else {
                    List.empty[String]
                  }

                  fieldValues += ((value, sources))
                }

                resultBuilder += (field -> fieldValues.toList)
              }

              result = resultBuilder.result()
            } catch {
              case e: Exception =>
                logger.error(s"Error parsing field_data JSON: ${e.getMessage}")
                e.printStackTrace()
            }
          }
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error getting field sources for profile $profileId: ${e.getMessage}")
        e.printStackTrace()
    }

    result
  }

}
