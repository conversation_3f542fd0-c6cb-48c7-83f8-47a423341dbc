package com.vcc.bigdata.application.build

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.typesafe.scalalogging.Logger
import com.vcc.bigdata.application.model.{FieldSuggestData, SourceMapping, Unification}
import com.vcc.bigdata.config.AppConfig
import com.vcc.bigdata.connector.kafka.{KafkaConfiguration, KafkaPublisherProperties, UnifyDataMessagePublisher}
import com.vcc.bigdata.infrastructure.APIHelper
import com.vcc.bigdata.utility.Strings
import org.apache.spark.sql.{SQLContext, SparkSession}
import org.apache.spark.{SparkConf, SparkContext}
import org.json.{JSONArray, JSONObject}

import java.sql.DriverManager
import scala.collection.mutable.ListBuffer

/*
 * This class is intended for testing the new unification process
 * which involves storing data only in apache doris as a replacement for
 * the current data storage systems (HBase, Elasticsearch, TiDB).
 */
class UnificationNew extends Serializable {
  // Initialize the necessary configurations and resources
  System.setProperty("HADOOP_USER_NAME", "cdp")

  val logger: Logger = Logger(classOf[UnificationNew])

  var sparkConf: SparkConf = _
  @transient
  var sqlContext: SQLContext = _
  @transient
  var sparkContext: SparkContext = _
  @transient
  var sparkSession: SparkSession = _

//  @transient
//  private val dorisConnector: DorisConnector = new DorisConnector(AppConfig.getProperties("doris.datasource.url"),
//    AppConfig.getProperties("doris.datasource.username"),
//    AppConfig.getProperties("doris.datasource.password"))


  def init(args: Array[String]): Unit = {
      val test: Boolean = if (args.length > 0) args(0).toBoolean else true
      val unificationId: String = if (args.length > 1) args(1) else ""

      if (Strings.isNullOrEmpty(unificationId)) {
        logger.error("unificationId input not found")
        sys.exit(-1)
      }

      sparkConf = new SparkConf()
      sparkConf.set("doris.fenodes", AppConfig.getProperties("doris.fenodes"))
      sparkConf.set("doris.table.identifier", AppConfig.getProperties("doris.database") + "." + AppConfig.getProperties("doris.table"))
      sparkConf.set("doris.user", AppConfig.getProperties("doris.user"))
      sparkConf.set("doris.password", AppConfig.getProperties("doris.password"))

      if (test) {
        sparkConf.setAppName("Unification Source Normal")
        sparkConf.setMaster("local[*]")
        println("Run mode test!")
      } else {
        sparkConf.setMaster("yarn")
        sparkConf.set("spark.yarn.queue", AppConfig.getProperties("spark.yarn.queue"))
        sparkConf.set("spark.sql.broadcastTimeout", "-1")
      }

      sparkContext = new SparkContext(sparkConf)
      sparkContext.setLogLevel("WARN")

      sparkSession = SparkSession.builder()
        .config(sparkContext.getConf)
        .getOrCreate()

      // initialize sqlContext
      sqlContext = sparkSession.sqlContext
      sparkConf.set("unification_id", unificationId)
  }

  def run(): Unit = {
    logger.info("=============================================")
    logger.info("Start application!")
    val startTime = System.currentTimeMillis()
    val unificationId: String = sparkConf.get("unification_id")

    logger.info("=============== DEBUG ==============================")
    logger.info(s"unification id: ${unificationId}")

    // Update source's unify status: 1-unifying, 2-done, -1-deleted
    // Get detail source unification's information
    val unification: Unification = APIHelper.apply().updateUnification(unificationId, 1)
    // Get source's mapping
    val sourceMapping: Map[String, SourceMapping] = APIHelper.apply().getSourceMapping(unification.sourceId)
    val rule: JSONObject = new JSONObject(unification.ruleUnification)
    // Logic: and, or,...
    val ex: String = rule.getString("ex")
    // fields: list field trong logic unify
    var fields: List[String] = List()
    for (i <- 0 until rule.getJSONArray("field").length()) fields = fields :+ rule.getJSONArray("field").get(i).toString

    // Fields contain contact information
    // contactMap bao gồm thông tin email và phone (thêm trường emails và phones đối với bizfly crm)
    var contactMap: Map[String, String] = Map()
    // Fields contain abbreviations that suggest information such as Mr, Ms, HN, HaNoi, Doanh Nghiep, Cty,...
    var fieldSelectMap: Map[String, FieldSuggestData] = Map()
    unification.mappingConfirm.foreach(mappingConfirm => {
      if (mappingConfirm._2.demographic.equals("phone")) contactMap = contactMap + (mappingConfirm._1 -> "phone")
      if (mappingConfirm._2.demographic.equals("email")) contactMap = contactMap + (mappingConfirm._1 -> "email")
      if (mappingConfirm._2.fieldSource.equals("phones")) contactMap = contactMap + (mappingConfirm._1 -> "phones")
      if (mappingConfirm._2.fieldSource.equals("emails")) contactMap = contactMap + (mappingConfirm._1 -> "emails")
      if (mappingConfirm._2.fieldDetect != null) {
        fieldSelectMap = fieldSelectMap + (mappingConfirm._1 -> APIHelper.apply().getDataDetectByFieldSource(mappingConfirm._1, mappingConfirm._2.fieldDetect))
      }
    })

    // Fetch raw data directly from Doris in a single query
    val rawData = fetchRawDataDirectly(unification.userIdSource, unification.sourceId)
    logger.info(s"Fetched ${rawData.size} raw data records directly")

    // Process data in parallel using simple chunking
    val batchSize = 10000
    val batches = if (rawData.nonEmpty) rawData.grouped(batchSize).toList else List.empty
    println(s"Divided data into ${batches.size} batches")
    val rdd = sparkSession.sparkContext.parallelize(batches)

    // Process data in parallel
    rdd.foreachPartition(partition => {
      logger.info(s"Processing partition with ${partition.size} groups")

      // Initialize Kafka publisher
      @transient
      val unifyDataKafkaPublisher: UnifyDataMessagePublisher = {
        val kafkaConfig = new KafkaConfiguration(
          AppConfig.getProperties("kafka.publisher.bootstrap-servers"),
          AppConfig.getProperties("kafka.publisher.unify-topic"),
          AppConfig.getProperties("kafka.publisher.username"),
          AppConfig.getProperties("kafka.publisher.password")
        )

        val properties = KafkaPublisherProperties.producerProperties(kafkaConfig)
        val publisher = new UnifyDataMessagePublisher(kafkaConfig, properties)
        publisher
      }

      // Get data type mappings
      val dataTypeMap: Map[String, String] = APIHelper.apply().getDataTypeProfileManagementV2(unification.userIdProfileManagement)
      val mapper: ObjectMapper = new ObjectMapper()
      mapper.registerModule(DefaultScalaModule)

      // Process each batch of raw data
      partition.foreach(rawDataBatch => {
        logger.info(s"Processing batch with ${rawDataBatch.size} raw data records")

        // Skip PHASE 1 (unifying profiles within the source)
        // Convert raw data directly to schema format for Phase 2

        // PHASE 2: Merge raw data records directly into overall profiles
        logger.info("PHASE 2: Merging raw data records into overall profiles")

        var profileAppend: List[Map[String, List[Object]]] = List()
        var profileMerge: Map[String, Map[String, Set[Object]]] = Map()

        // Collect query conditions for batch processing
        val batchQueryConditions = ListBuffer[(String, Map[String, Object])]()
        val recordProfiles = scala.collection.mutable.Map[String, Map[String, List[Object]]]()

        // Process each raw data record directly
        rawDataBatch.foreach { rawData =>
          val recordId = rawData._1
          val fieldDataMap = rawData._2

          // Convert to schema format
          var rawdataSchema: Map[String, List[Object]] = Map()

          // Build query conditions for profile matching
          var queryConditions: Map[String, Object] = Map()

          fieldDataMap.foreach { case (fieldSource, value) =>
            if (unification.mappingConfirm.contains(fieldSource)) {
              val fieldName = unification.mappingConfirm(fieldSource).fieldSource
              val dataType = unification.mappingConfirm(fieldSource).dataType

              // Process value based on data type
              val processedValue: Object = value

              // Add to schema
              if (!rawdataSchema.contains(fieldName)) rawdataSchema = rawdataSchema + (fieldName -> List())
              rawdataSchema = rawdataSchema + (fieldName -> (rawdataSchema(fieldName) :+ processedValue).distinct)

              // Add to query conditions if it's a field used for matching
              if (isKeyInFields(fieldSource, fields)) {
                queryConditions = queryConditions + (fieldName -> processedValue)
              }
            }
          }

          // Store the profile data for this record
          recordProfiles(recordId) = rawdataSchema

          // If we have query conditions, add to batch
          if (queryConditions.nonEmpty) {
            batchQueryConditions += ((recordId, queryConditions))
          } else if (rawdataSchema.nonEmpty) {
            // No query conditions, append directly
            profileAppend = profileAppend :+ rawdataSchema
            logger.info(s"No query conditions available for record $recordId, will append as new profile")
          }
        }

        // Second pass: batch process all query conditions
        if (batchQueryConditions.nonEmpty) {
          try {
            println(s"Searching for matching profiles in batch for ${batchQueryConditions.size} records")
//            val batchResults = findMatchingProfilesBatch(fields, ex, batchQueryConditions.toList, unification.userIdProfileManagement)
            val batchResults = Map.empty[String, List[(String, Map[String, String])]]

            // Process batch results
            batchQueryConditions.foreach { case (recordId, _) =>
              val rawdataSchema = recordProfiles(recordId)

              if (!batchResults.contains(recordId) || batchResults(recordId).isEmpty) {
                // No matching profile found, append new profile
                profileAppend = profileAppend :+ rawdataSchema
                println(s"No matching profile found for record $recordId, will append as new profile")
              }
              else {
                // Matching profile found, prepare for merge
                val matchingProfiles = batchResults(recordId)
                val profileId = matchingProfiles.head._1
                val profileData = matchingProfiles.head._2

                logger.info(s"Found matching profile $profileId for record $recordId with ${profileData.size} fields")

                if (!profileMerge.contains(profileId)) profileMerge = profileMerge + (profileId -> Map())

                var finalRawdata = profileMerge(profileId)

                // Convert profileData from Map[String, String] to the format needed for merging
                val convertedProfileData = profileData.map { case (field, value) =>
                  field -> List[Object](value)
                }

                // Merge the converted profile data
                convertedProfileData.foreach(entry => {
                  if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                  finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
                })

                // Merge the raw data schema
                rawdataSchema.foreach(entry => {
                  if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                  finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
                })

                profileMerge = profileMerge + (profileId -> finalRawdata)
                logger.info(s"Merged raw data from record $recordId into existing profile $profileId")
              }
            }
          } catch {
            case e: Exception =>
              logger.error(s"Error when searching for matching profiles in batch: ${e.getMessage}")
              e.printStackTrace()

              // Fallback to individual processing if batch processing fails
              logger.info("Falling back to individual profile processing")
              batchQueryConditions.foreach { case (recordId, queryConditions) =>
                try {
                  val matchingProfiles = findMatchingProfiles(fields, ex, queryConditions, unification.userIdProfileManagement)
                  val rawdataSchema = recordProfiles(recordId)

                  if (matchingProfiles.isEmpty) {
                    // No matching profile found, append new profile
                    profileAppend = profileAppend :+ rawdataSchema
                    logger.info(s"No matching profile found for record $recordId, will append as new profile")
                  } else {
                    // Matching profile found, prepare for merge
                    val profileId = matchingProfiles.head._1
                    val profileData = matchingProfiles.head._2

                    logger.info(s"Found matching profile $profileId for record $recordId with ${profileData.size} fields")

                    if (!profileMerge.contains(profileId)) profileMerge = profileMerge + (profileId -> Map())

                    var finalRawdata = profileMerge(profileId)

                    // Convert profileData from Map[String, String] to the format needed for merging
                    val convertedProfileData = profileData.map { case (field, value) =>
                      field -> List[Object](value)
                    }.toMap

                    // Merge the converted profile data
                    convertedProfileData.foreach(entry => {
                      if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                      finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
                    })

                    // Merge the raw data schema
                    rawdataSchema.foreach(entry => {
                      if (!finalRawdata.contains(entry._1)) finalRawdata = finalRawdata + (entry._1 -> Set())
                      finalRawdata = finalRawdata + (entry._1 -> (finalRawdata(entry._1) ++ entry._2))
                    })

                    profileMerge = profileMerge + (profileId -> finalRawdata)
                    logger.info(s"Merged raw data from record $recordId into existing profile $profileId")
                  }
                } catch {
                  case e: Exception =>
                    logger.error(s"Error when searching for matching profiles for record $recordId: ${e.getMessage}")
                    e.printStackTrace()
                    // Add to append list as fallback
                    profileAppend = profileAppend :+ recordProfiles(recordId)
                }
              }
          }
        }

        // Process append
        if (profileAppend.nonEmpty) {
          println(s"Appending ${profileAppend.size} new profiles")
          appendProfile(unification.userIdProfileManagement, dataTypeMap, profileAppend, unification.sourceId, unifyDataKafkaPublisher)
        }

        // Process merge
        if (profileMerge.nonEmpty) {
          println(s"Merging ${profileMerge.size} existing profiles")
          mergeProfile(unification.userIdProfileManagement, dataTypeMap, unification.sourceId, profileMerge, unifyDataKafkaPublisher)
        }
      })
    })

    logger.info(s"Process completed in ${System.currentTimeMillis() - startTime} ms")

    // Update unification status
    APIHelper.apply().unificationSuccess(unificationId)
  }

  /**
   * Method 3: Use JDBC to execute a completely custom SQL query directly against Doris
   * This bypasses the Spark-Doris connector entirely and gives you full SQL flexibility
   */
  private def executeJdbcQuery(userIdSource: String, sourceId: String) = {
    // Extract Doris connection information
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Define your custom SQL query
    val customQuery = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.table")}
      LIMIT 100
    """

    logger.info(s"Executing JDBC query: $customQuery")

    // Use Spark's JDBC data source to execute the query
    sparkSession.read
      .format("jdbc")
      .option("url", jdbcUrl)
      .option("user", jdbcUser)
      .option("password", jdbcPassword)
      .option("driver", "com.mysql.jdbc.Driver")
      .option("dbtable", s"(${customQuery}) as custom_query")
      .load()
  }

  /**
   * Fetch raw data directly from Doris
   * @param userId User ID
   * @param sourceId Source ID
   * @return List of (recordId, data) tuples where data contains all fields from the record
   */
  def fetchRawDataDirectly(userId: String, sourceId: String): List[(String, Map[String, String])] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.raw_data_table") + userId + "_" + sourceId}
    """

    logger.info(s"Fetching raw data directly with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      // For demonstration, we'll use JDBC directly instead of Spark
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val recordId = resultSet.getString("_row_key")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns except id, user_id, and source_id
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName == "field_data") {
              val columnValueJson = resultSet.getString(i)
              if (columnValueJson != null) {
                val columnValueObj = new JSONObject(columnValueJson)
                val keys = columnValueObj.keys()
                while (keys.hasNext) {
                  val key = keys.next()
                  val value = columnValueObj.getString(key)
                  dataMap += (key -> value)
                }
              }
            }
          }

          result += ((recordId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error fetching raw data: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Fetched ${result.size} raw data records")
    result.toList
  }

  /**
   * Get raw data from Doris for a list of record IDs (kept for backward compatibility)
   * @param userId User ID
   * @param sourceId Source ID
   * @param recordIds List of record IDs
   * @return List of (recordId, data) tuples where data contains all fields from the record
   */
  def getRawDataFromDoris(userId: String, sourceId: String, recordIds: List[String]): List[(String, Map[String, String])] = {
    if (recordIds.isEmpty) return List.empty[(String, Map[String, String])]

    // For better performance, we could use the fetchRawDataDirectly method and filter the results
    // However, for large datasets, it's more efficient to query only the specific records we need
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Create a comma-separated list of record IDs for the IN clause
    val recordIdsStr = recordIds.map(id => s"'$id'").mkString(", ")

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.raw_data_table") + userId + "_" + sourceId}
      WHERE user_id = '$userId' AND source_id = '$sourceId' AND id IN ($recordIdsStr)
    """

    logger.info(s"Getting raw data with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val recordId = resultSet.getString("id")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns except id, user_id, and source_id
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName != "id" && columnName != "user_id" && columnName != "source_id") {
              val columnValue = resultSet.getString(i)
              if (columnValue != null) {
                dataMap += (columnName -> columnValue)
              }
            }
          }

          result += ((recordId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error getting raw data: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Retrieved ${result.size} raw data records")
    result.toList
  }

  /**
   * Find matching profiles in Doris
   * @param fields Fields to match
   * @param ex Logic (and/or)
   * @param queryConditions Query conditions
   * @param userId User ID for profile management
   * @return List of (profileId, Map[String, String]) tuples
   */
  def findMatchingProfiles(fields: List[String], ex: String, queryConditions: Map[String, Object], userId: String): List[(String, Map[String, String])] = {
    if (queryConditions.isEmpty) return List.empty[(String, Map[String, String])]

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Check if field_data column exists
    var useFieldData = false
    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val metaDataStatement = connection.createStatement()
        val tableMetaQuery = s"DESCRIBE ${AppConfig.getProperties("doris.profile_person_table") + userId}"
        val metaDataResultSet = metaDataStatement.executeQuery(tableMetaQuery)

        var foundFieldData = false
        while (metaDataResultSet.next() && !foundFieldData) {
          val columnName = metaDataResultSet.getString("Field")
          if (columnName == "field_data") {
            useFieldData = true
            foundFieldData = true
          }
        }
        metaDataResultSet.close()
        metaDataStatement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error checking for field_data column: ${e.getMessage}")
        e.printStackTrace()
    }

    // Build WHERE clause based on query conditions and ex (AND/OR)
    val whereClause = if (useFieldData) {
      // For field_data column, use JSON_CONTAINS to query nested fields
      queryConditions.map { case (field, value) =>
        val valueStr = value match {
          case s: String => s"'${s.replace("'", "''").replace("\\", "\\\\")}'"
          case n: Number => s"'${n.toString}'"
          case _ => "'null'"
        }
        s"JSON_CONTAINS(JSON_EXTRACT(field_data, '$$.${field}[*].value'), $valueStr)"
      }.mkString(if (ex == "and") " AND " else " OR ")
    } else {
      // For normal table format, directly compare columns
      queryConditions.map { case (field, value) =>
        val valueStr = value match {
          case s: String => s"'$s'"
          case n: Number => n.toString
          case _ => "NULL"
        }
        s"$field = $valueStr"
      }.mkString(if (ex == "and") " AND " else " OR ")
    }

    val query = s"""
      SELECT *
      FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
      WHERE $whereClause
      LIMIT 1
    """

    logger.info(s"Finding matching profiles with query: $query")

    val result: ListBuffer[(String, Map[String, String])] = ListBuffer.empty[(String, Map[String, String])]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)
        val metaData = resultSet.getMetaData
        val columnCount = metaData.getColumnCount

        while (resultSet.next()) {
          val profileId = resultSet.getString("profile_id")
          val dataMap = Map.newBuilder[String, String]

          // Extract all columns
          for (i <- 1 to columnCount) {
            val columnName = metaData.getColumnName(i)
            if (columnName != "profile_id") {
              val columnValue = resultSet.getString(i)
              if (columnValue != null) {
                dataMap += (columnName -> columnValue)
              }
            }
          }

          // If using field_data, extract fields from JSON
          if (useFieldData) {
            val fieldDataStr = resultSet.getString("field_data")
            if (fieldDataStr != null && fieldDataStr.nonEmpty) {
              try {
                val fieldDataJson = new JSONObject(fieldDataStr)
                val keys = fieldDataJson.keys()
                while (keys.hasNext()) {
                  val key = keys.next()
                  val valuesArray = fieldDataJson.getJSONArray(key)
                  if (valuesArray.length() > 0) {
                    // Use the first value's "value" field for simplicity
                    val valueObj = valuesArray.getJSONObject(0)
                    dataMap += (key -> valueObj.getString("value"))
                  }
                }
              } catch {
                case e: Exception =>
                  logger.error(s"Error parsing field_data JSON: ${e.getMessage}")
              }
            }
          }

          result += ((profileId, dataMap.result()))
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error finding matching profiles: ${e.getMessage}")
        e.printStackTrace()
    }

    logger.info(s"Found ${result.size} matching profiles")
    result.toList
  }

  def isTableEmpty(userId: String, tableNamePrefix: String): Boolean = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        val statement = connection.createStatement()
        val query = s"SELECT COUNT(*) FROM ${AppConfig.getProperties(tableNamePrefix + userId)}"
        val resultSet = statement.executeQuery(query)

        if (resultSet.next()) {
          val count = resultSet.getInt(1)
          count == 0
        } else {
          true
        }
      } finally {
        connection.close()
      }
    }
    }

  /**
   * Find matching profiles in Doris in batch
   * @param fields Fields to match
   * @param ex Logic (and/or)
   * @param batchQueryConditions List of (groupId, queryConditions) tuples
   * @param userId User ID for profile management
   * @param batchSize Maximum number of conditions to include in a single query
   * @return Map of groupId to List of (profileId, Map[String, String]) tuples
   */
  def findMatchingProfilesBatch(
    fields: List[String],
    ex: String,
    batchQueryConditions: List[(String, Map[String, Object])],
    userId: String,
    batchSize: Int = 50
  ): Map[String, List[(String, Map[String, String])]] = {
    if (batchQueryConditions.isEmpty) return Map.empty[String, List[(String, Map[String, String])]]

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    // Process each group separately and combine results
    // This is more reliable than using CASE statements to track conditions
    val allResults = scala.collection.mutable.Map[String, List[(String, Map[String, String])]]()

    // Process in batches to avoid too many database connections
    batchQueryConditions.grouped(batchSize).foreach { batchConditions =>
      logger.info(s"Processing batch of ${batchConditions.size} conditions")

      // Process each group in the batch
      batchConditions.foreach { case (groupId, conditions) =>
        if (conditions.isEmpty) {
          logger.info(s"Skipping group $groupId with empty conditions")
          allResults(groupId) = List.empty[(String, Map[String, String])]
        } else {
          // Build lateral view for each needed field inside field_data
          val lateralViews = fields.map(field => s"lateral view explode_json_array_json(field_data['$field']) tmp as ${field.replace(" ", "_")}_entry")
            .mkString("\n")

          // Build WHERE clause for this group's conditions
          val whereClause = conditions.map { case (field, value) =>
            val valueStr = value match {
              case s: String => s"'${s.replace("'", "''").replace("\\", "\\\\")}'"
              case n: Number => s"${n.toString}"
              case _ => "'null'"
            }
            s"""${field.replace(" ", "_")}_entry['value'] = '${valueStr.replace("'", "\"")}' """
          }.mkString(if (ex == "and") " AND " else " OR ")

          val query = s"""
            SELECT *
            FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
            $lateralViews
            WHERE ($whereClause) and is_deleted = 0
          """

          logger.info(s"Finding matching profiles for group $groupId with query: $query")
          println(s"Finding matching profiles for group $groupId with query: $query")

          val groupResults = ListBuffer[(String, Map[String, String])]()

          try {
            Class.forName("com.mysql.jdbc.Driver")
            val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
            try {
              val statement = connection.createStatement()
              val resultSet = statement.executeQuery(query)
              val metaData = resultSet.getMetaData
              val columnCount = metaData.getColumnCount

              while (resultSet.next()) {
                val profileId = resultSet.getString("profile_id")
                val dataMap = Map.newBuilder[String, String]

                // Extract all columns except profile_id
                for (i <- 1 to columnCount) {
                  val columnName = metaData.getColumnName(i)
                  if (columnName != "profile_id" && columnName != "field_data") {
                    val columnValue = resultSet.getString(i)
                    if (columnValue != null) {
                      dataMap += (columnName -> columnValue)
                    }
                  }
                }

                // If using field_data, extract fields from JSON
                val fieldDataStr = resultSet.getString("field_data")
                if (fieldDataStr != null && fieldDataStr.nonEmpty && fieldDataStr.length() > 2) {
                  try {
                    val fieldDataJson = new JSONObject(fieldDataStr)
                    val keys = fieldDataJson.keys()
                    while (keys.hasNext()) {
                      val key = keys.next()
                      val valuesArray = fieldDataJson.getJSONArray(key)
                      if (valuesArray.length() > 0) {
                        // Use the first value's "value" field for simplicity
                        val valueObj = valuesArray.getJSONObject(0)
                        dataMap += (key -> valueObj.getString("value"))
                      }
                    }
                  } catch {
                    case e: Exception =>
                      logger.error(s"Error parsing field_data JSON: ${e.getMessage} - $fieldDataStr")
                      println(s"Error resultSet.getString: ${resultSet.getString("profile_id")}")
                      println(s"Error parsing query JSON: ${e.getMessage} - $query")
                      System.exit(1)
                  }
                }

                groupResults += ((profileId, dataMap.result()))
              }

              resultSet.close()
              statement.close()
            } finally {
              connection.close()
            }
          } catch {
            case e: Exception =>
              logger.error(s"Error finding matching profiles for group $groupId: ${e.getMessage}")
              e.printStackTrace()
          }

          // Store results for this group
          allResults(groupId) = groupResults.toList
          logger.info(s"Found ${groupResults.size} matching profiles for group $groupId")
        }
      }
    }

    // Convert mutable map to immutable map for return
    val finalResults = allResults.toMap

    logger.info(s"Found matching profiles for ${finalResults.size} groups out of ${batchQueryConditions.size} total groups")
    finalResults
  }

  /**
   * Append new profiles to Doris
   * @param userId User ID
   * @param dataTypeMap Data type map
   * @param profileData Profile data
   * @param sourceId Source ID
   * @param unifyDataKafkaPublisher Kafka publisher
   * @param batchSize Maximum number of profiles to insert in a single query
   */
  def appendProfile(
    userId: String,
    dataTypeMap: Map[String, String],
    profileData: List[Map[String, List[Object]]],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = 100
  ): Unit = {
    if (profileData.isEmpty) return

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Process in batches to avoid overly large queries
        profileData.grouped(batchSize).foreach { batchData =>
          // Prepare profile IDs and Kafka messages for this batch
          val profileIds = batchData.map(_ => java.util.UUID.randomUUID().toString)
          val timestamp = System.currentTimeMillis()

          // Build the batch INSERT statement
          val insertPrefix = s"""
            INSERT INTO ${AppConfig.getProperties("doris.profile_person_table") + userId}
            (profile_id, last_update, sources, field_data)
            VALUES
          """

          val valuesList = batchData.zip(profileIds).map { case (data, profileId) =>
            // Convert profile data to JSON structure
            val fieldDataJson = new JSONObject()

            data.foreach { case (field, values) =>
              if (values.nonEmpty) {
                // Store values as an array of objects with source information in the field_data
                val valuesArray = new JSONArray()
                values.foreach { value =>
                  // Create an object for each value with the value and its source
                  val valueObj = new JSONObject()
                  valueObj.put("value", value.toString)

                  // Add source information as a concatenated string
                  valueObj.put("sources", sourceId)

                  valuesArray.put(valueObj)
                }
                fieldDataJson.put(field, valuesArray)
              }
            }

            // Create the VALUES tuple
            val jsonString = escapeJsonForSql(fieldDataJson.toString)
            s"""('$profileId', $timestamp, '["$sourceId"]', $jsonString)"""
          }

          // Execute the batch INSERT
          val sql = insertPrefix + valuesList.mkString(",\n")

          logger.info(s"Appending ${batchData.size} profiles with batch query")
          val statement = connection.createStatement()
          statement.execute(sql)
          statement.close()

          // Publish to Kafka
          batchData.zip(profileIds).foreach { case (_, profileId) =>
            val kafkaInfo = new JSONObject()
            kafkaInfo.put("profile_id", profileId)
            kafkaInfo.put("is_delete", 0)
            kafkaInfo.put("sources", new JSONArray().put(sourceId))
            kafkaInfo.put("user_id", userId)
            unifyDataKafkaPublisher.publish(kafkaInfo.toString())
          }
        }
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error appending profiles: ${e.getMessage}")
        e.printStackTrace()
    }
  }

  /**
   * Merge profiles in Doris
   * @param userId User ID
   * @param dataTypeMap Data type map
   * @param sourceId Source ID
   * @param profileMerge Profile merge data
   * @param unifyDataKafkaPublisher Kafka publisher
   * @param batchSize Maximum number of profiles to fetch or update in a single query
   */
  def mergeProfile(
    userId: String,
    dataTypeMap: Map[String, String],
    sourceId: String,
    profileMerge: Map[String, Map[String, Set[Object]]],
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = 1000
  ): Unit = {
    if (profileMerge.isEmpty) return

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Process in batches to avoid overly large queries
        profileMerge.keys.grouped(batchSize).foreach { batchProfileIds =>
          // Fetch all profiles in this batch with a single query
          val profileIdList = batchProfileIds.map(id => s"'$id'").mkString(", ")
          val query = s"""
            SELECT profile_id, sources, field_data
            FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
            WHERE profile_id IN ($profileIdList)
          """

          logger.info(s"Fetching ${batchProfileIds.size} profiles with batch query")

          val statement = connection.createStatement()
          val resultSet = statement.executeQuery(query)

          // Process each profile and prepare updates
          val updateStatements = ListBuffer[String]()
          val kafkaMessages = ListBuffer[(String, List[String])]()

          // Map to store profile data by profile ID
          val profileDataMap = scala.collection.mutable.Map[String, (String, JSONObject)]()

          // Extract profile data
          while (resultSet.next()) {
            val profileId = resultSet.getString("profile_id")
            val sourcesStr = resultSet.getString("sources")
            val fieldDataStr = resultSet.getString("field_data")

            // Parse field_data JSON
            val fieldDataJson = if (fieldDataStr != null && fieldDataStr.nonEmpty) {
              new JSONObject(fieldDataStr)
            } else {
              new JSONObject()
            }

            profileDataMap += (profileId -> (sourcesStr, fieldDataJson))
          }

          resultSet.close()

          // Process each profile in the batch
          batchProfileIds.foreach { profileId =>
            if (profileDataMap.contains(profileId)) {
              val (currentSourcesStr, fieldDataJson) = profileDataMap(profileId)

              // Update sources
              val currentSources = if (currentSourcesStr != null && currentSourcesStr.nonEmpty) {
                val sourcesArray = new JSONArray(currentSourcesStr)
                val sourcesList = ListBuffer[String]()
                for (i <- 0 until sourcesArray.length()) {
                  sourcesList += sourcesArray.getString(i)
                }
                sourcesList.toList
              } else {
                List.empty[String]
              }

              val updatedSources = if (currentSources.contains(sourceId)) currentSources else currentSources :+ sourceId
//              val sourcesJson = new JSONArray(updatedSources.asJava).toString()

              // Merge new field data
              profileMerge(profileId).foreach { case (field, values) =>
                if (!field.equals("field_data")) return
                if (values.nonEmpty) {
                  // Get existing values for this field
                  val existingValuesMap = if (fieldDataJson.has(field)) {
                    val existingArray = fieldDataJson.getJSONArray(field)
                    val valuesMap = scala.collection.mutable.Map[String, Set[String]]()

                    for (i <- 0 until existingArray.length()) {
                      val valueObj = existingArray.getJSONObject(i)
                      val value = valueObj.getString("value")

                      // Extract sources for this value (stored as concatenated string)
                      val sourcesStr = valueObj.getString("sources")
                      val sources = if (sourcesStr.nonEmpty) {
                        sourcesStr.split("\\|").toSet
                      } else {
                        Set.empty[String]
                      }

                      valuesMap += (value -> sources)
                    }

                    valuesMap
                  } else {
                    scala.collection.mutable.Map.empty[String, Set[String]]
                  }

                  // Add new values with source information
                  values.foreach { value =>
                    val valueStr = value.toString
                    val updatedSources = if (existingValuesMap.contains(valueStr)) {
                      existingValuesMap(valueStr) + sourceId
                    } else {
                      Set(sourceId)
                    }
                    existingValuesMap(valueStr) = updatedSources
                  }

                  // Update the field in JSON
                  val newValuesArray = new JSONArray()
                  existingValuesMap.foreach { case (value, sources) =>
                    val valueObj = new JSONObject()
                    valueObj.put("value", value)

                    // Join sources with pipe delimiter
                    val sourcesStr = sources.mkString("|")
                    valueObj.put("sources", sourcesStr)

                    newValuesArray.put(valueObj)
                  }
                  fieldDataJson.put(field, newValuesArray)
                }
              }

              // Create UPDATE statement
              val updateSql = s"""
                UPDATE ${AppConfig.getProperties("doris.profile_person_table") + userId}
                SET
                  field_data = '${fieldDataJson.toString()}',
                  sources = '[${updatedSources.mkString(",")}]',
                  last_update = ${System.currentTimeMillis()}
                WHERE profile_id = '$profileId'
              """
              println("updateSql: " + updateSql)

              updateStatements += updateSql
              kafkaMessages += ((profileId, updatedSources))
            }
          }

          // Execute all updates
          if (updateStatements.nonEmpty) {
            logger.info(s"Executing ${updateStatements.size} profile updates")

            // Execute each update statement
            updateStatements.foreach { sql =>
              statement.execute(sql)
            }

            // Publish to Kafka
            kafkaMessages.foreach { case (profileId, updatedSources) =>
              val kafkaInfo = new JSONObject()
              kafkaInfo.put("profile_id", profileId)
              kafkaInfo.put("is_delete", 0)
              kafkaInfo.put("sources", s"[${updatedSources.mkString(",")}]")
              kafkaInfo.put("user_id", userId)
              unifyDataKafkaPublisher.publish(kafkaInfo.toString())
            }
          }

          statement.close()
        }
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error merging profiles: ${e.getMessage}")
        e.printStackTrace()
    }
  }

  /**
   * Check if a key is in the fields list
   * @param key Key to check
   * @param fields Fields list
   * @return True if the key is in the fields list
   */
  private def isKeyInFields(key: String, fields: List[String]): Boolean = {
    for (field <- fields) if (isKeyInField(key, field)) return true
    false
  }

  /**
   * Check if a key is in a field
   * @param key Key to check
   * @param field Field to check
   * @return True if the key is in the field
   */
  private def isKeyInField(key: String, field: String): Boolean = {
    if (field.equals(key)) return true
    if (field.contains(".")) {
      val fieldPath = field.split('.')
      if (fieldPath.contains(key)) return true
    }
    false
  }

  /**
   * Properly escapes a JSON string for inclusion in a SQL statement
   * @param json The JSON string to escape
   * @return Properly escaped string for SQL
   */
  private def escapeJsonForSql(json: String): String = {
    // First escape single quotes for SQL by doubling them
    val sqlEscaped = json.replace("'", "''")
    // Then wrap in single quotes for the SQL string
    s"'$sqlEscaped'"
  }

  /**
   * Remove a specific source from a profile
   * @param userId User ID
   * @param profileId Profile ID
   * @param sourceId Source ID to remove
   * @param unifyDataKafkaPublisher Kafka publisher
   * @return True if successful, false otherwise
   */
  def removeSourceFromProfile(
    userId: String,
    profileId: String,
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher
  ): Boolean = {
    removeSourceFromProfiles(userId, List(profileId), sourceId, unifyDataKafkaPublisher).contains(profileId)
  }

  /**
   * Remove a specific source from multiple profiles
   * @param userId User ID
   * @param profileIds List of profile IDs
   * @param sourceId Source ID to remove
   * @param unifyDataKafkaPublisher Kafka publisher
   * @return Set of successfully processed profile IDs
   */
  def removeSourceFromProfiles(
    userId: String,
    profileIds: List[String],
    sourceId: String,
    unifyDataKafkaPublisher: UnifyDataMessagePublisher,
    batchSize: Int = 100
  ): Set[String] = {
    if (profileIds.isEmpty) return Set.empty[String]

    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val successfulProfiles = scala.collection.mutable.Set[String]()

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Process in batches to avoid overly large queries
        profileIds.grouped(batchSize).foreach { batchProfileIds =>
          // Fetch all profiles in this batch with a single query
          val profileIdList = batchProfileIds.map(id => s"'$id'").mkString(", ")
          val query = s"""
            SELECT profile_id, sources, field_data
            FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
            WHERE profile_id IN ($profileIdList)
          """

          logger.info(s"Fetching ${batchProfileIds.size} profiles to remove source $sourceId")

          val statement = connection.createStatement()
          val resultSet = statement.executeQuery(query)

          // Store profile data for processing
          val profileDataMap = scala.collection.mutable.Map[String, (List[String], JSONObject)]()

          // Extract profile data
          while (resultSet.next()) {
            val profileId = resultSet.getString("profile_id")
            val sourcesStr = resultSet.getString("sources")
            val fieldDataStr = resultSet.getString("field_data")

            // Parse sources JSON
            val currentSources = if (sourcesStr != null && sourcesStr.nonEmpty) {
              val sourcesArray = new JSONArray(sourcesStr)
              val sourcesList = ListBuffer[String]()
              for (i <- 0 until sourcesArray.length()) {
                sourcesList += sourcesArray.getString(i)
              }
              sourcesList.toList
            } else {
              List.empty[String]
            }

            // Parse field_data JSON
            val fieldDataJson = if (fieldDataStr != null && fieldDataStr.nonEmpty) {
              new JSONObject(fieldDataStr)
            } else {
              new JSONObject()
            }

            // Only process profiles that contain the source
            if (currentSources.contains(sourceId)) {
              profileDataMap += (profileId -> (currentSources, fieldDataJson))
            } else {
              logger.info(s"Source $sourceId not found in profile $profileId, skipping")
            }
          }

          resultSet.close()

          // Prepare statements for batch execution
          val deleteStatements = ListBuffer[String]()
          val updateStatements = ListBuffer[String]()
          val kafkaMessages = ListBuffer[(String, Boolean, List[String])]() // (profileId, isDelete, sources)

          // Process each profile
          profileDataMap.foreach { case (profileId, (currentSources, fieldDataJson)) =>
            // Remove the source from the sources list
            val updatedSources = currentSources.filter(_ != sourceId)

            // Remove the source from each field value
            val keys = fieldDataJson.keys()
            while (keys.hasNext()) {
              val field = keys.next()
              val valuesArray = fieldDataJson.getJSONArray(field)
              val updatedValuesArray = new JSONArray()

              // Process each value in the field
              for (i <- 0 until valuesArray.length()) {
                val valueObj = valuesArray.getJSONObject(i)
                val sourcesStr = valueObj.getString("sources")

                // Split the sources string and filter out the removed source
                val sources = sourcesStr.split("\\|")
                val updatedSources = sources.filter(_ != sourceId)

                // Only keep values that still have sources after removal
                if (updatedSources.nonEmpty) {
//                  valueObj.put("sources", updatedSources.mkString("|")
//                  updatedValuesArray.put(valueObj)
                }
              }

              // Update the field with the filtered values
              fieldDataJson.put(field, updatedValuesArray)
            }

            // If no sources remain, delete the profile
            if (updatedSources.isEmpty) {
              val deleteSql = s"""
                DELETE FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
                WHERE profile_id = '$profileId'
              """

              deleteStatements += deleteSql
              kafkaMessages += ((profileId, true, List.empty[String]))
              logger.info(s"Preparing to delete profile $profileId as it has no remaining sources")
            } else {
              // Update the profile with the modified data
              val updateSql = s"""
                UPDATE ${AppConfig.getProperties("doris.profile_person_table") + userId}
                SET
                  field_data = '${escapeJsonForSql(fieldDataJson.toString())}',
                  sources = '[${updatedSources.mkString(",")}]',
                  last_update = ${System.currentTimeMillis()}
                WHERE profile_id = '$profileId'
              """

              updateStatements += updateSql
              kafkaMessages += ((profileId, false, updatedSources))
              logger.info(s"Preparing to update profile $profileId after removing source $sourceId")
            }

            // Mark as successful
            successfulProfiles += profileId
          }

          // Execute all statements
          if (deleteStatements.nonEmpty || updateStatements.nonEmpty) {
            val batchStatement = connection.createStatement()

            // Execute delete statements
            deleteStatements.foreach { sql =>
              try {
                batchStatement.execute(sql)
              } catch {
                case e: Exception =>
                  logger.error(s"Error executing delete statement: ${e.getMessage}")
                  e.printStackTrace()
              }
            }

            // Execute update statements
            updateStatements.foreach { sql =>
              try {
                batchStatement.execute(sql)
              } catch {
                case e: Exception =>
                  logger.error(s"Error executing update statement: ${e.getMessage}")
                  e.printStackTrace()
              }
            }

            batchStatement.close()

            // Publish to Kafka
            kafkaMessages.foreach { case (profileId, isDelete, updatedSources) =>
              val kafkaInfo = new JSONObject()
              kafkaInfo.put("profile_id", profileId)
              kafkaInfo.put("is_delete", if (isDelete) 1 else 0)
              kafkaInfo.put("sources", if (isDelete) "[]" else s"[${updatedSources.mkString(",")}]")
              kafkaInfo.put("user_id", userId)
              unifyDataKafkaPublisher.publish(kafkaInfo.toString())
            }
          }
        }
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error removing source $sourceId from profiles: ${e.getMessage}")
        e.printStackTrace()
    }

    successfulProfiles.toSet
  }

  /**
   * Find all profiles that contain a specific source
   * @param userId User ID
   * @param sourceId Source ID to find
   * @param limit Maximum number of profiles to return (0 for no limit)
   * @return List of profile IDs
   */
  def findProfilesBySource(
    userId: String,
    sourceId: String,
    limit: Int = 0
  ): List[String] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    val profileIds = ListBuffer[String]()

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Build query to find profiles containing the source
        val limitClause = if (limit > 0) s"LIMIT $limit" else ""
        val query = s"""
          SELECT profile_id
          FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
          WHERE JSON_CONTAINS(sources, '"$sourceId"')
          $limitClause
        """

        logger.info(s"Finding profiles containing source $sourceId")

        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)

        while (resultSet.next()) {
          profileIds += resultSet.getString("profile_id")
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error finding profiles by source $sourceId: ${e.getMessage}")
        e.printStackTrace()
    }

    profileIds.toList
  }

  /**
   * Get field values with their sources for a specific profile
   * @param userId User ID
   * @param profileId Profile ID
   * @param fieldName Field name (optional, if not provided returns all fields)
   * @return Map of field names to list of (value, sources) tuples
   */
  def getProfileFieldSources(
    userId: String,
    profileId: String,
    fieldName: Option[String] = None
  ): Map[String, List[(String, List[String])]] = {
    val jdbcUrl = s"jdbc:mysql://${AppConfig.getProperties("doris.fenodes").split(":")(0)}:9030/${AppConfig.getProperties("doris.database")}"
    val jdbcUser = AppConfig.getProperties("doris.user")
    val jdbcPassword = AppConfig.getProperties("doris.password")

    var result = Map.empty[String, List[(String, List[String])]]

    try {
      Class.forName("com.mysql.jdbc.Driver")
      val connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
      try {
        // Build query to get the profile
        val fieldSelector = fieldName.map(f => s"JSON_EXTRACT(field_data, '$$.${f}')").getOrElse("field_data")
        val query = s"""
          SELECT $fieldSelector as field_data
          FROM ${AppConfig.getProperties("doris.profile_person_table") + userId}
          WHERE profile_id = '$profileId'
        """

        logger.info(s"Getting field sources for profile $profileId${fieldName.map(f => s" field $f").getOrElse("")}")

        val statement = connection.createStatement()
        val resultSet = statement.executeQuery(query)

        if (resultSet.next()) {
          val fieldDataStr = resultSet.getString("field_data")

          if (fieldDataStr != null && fieldDataStr.nonEmpty) {
            try {
              // Parse the field data JSON
              val fieldDataJson = if (fieldName.isDefined) {
                // If a specific field was requested, the result is already the field array
                val fieldArray = new JSONArray(fieldDataStr)
                val singleFieldJson = new JSONObject()
                singleFieldJson.put(fieldName.get, fieldArray)
                singleFieldJson
              } else {
                // Otherwise, it's the full field_data object
                new JSONObject(fieldDataStr)
              }

              // Extract field values and sources
              val keys = fieldDataJson.keys()
              val resultBuilder = Map.newBuilder[String, List[(String, List[String])]]

              while (keys.hasNext()) {
                val field = keys.next()
                val valuesArray = fieldDataJson.getJSONArray(field)
                val fieldValues = ListBuffer[(String, List[String])]()

                for (i <- 0 until valuesArray.length()) {
                  val valueObj = valuesArray.getJSONObject(i)
                  val value = valueObj.getString("value")

                  // Extract sources from concatenated string
                  val sourcesStr = valueObj.getString("sources")
                  val sources = if (sourcesStr.nonEmpty) {
                    sourcesStr.split("\\|").toList
                  } else {
                    List.empty[String]
                  }

                  fieldValues += ((value, sources))
                }

                resultBuilder += (field -> fieldValues.toList)
              }

              result = resultBuilder.result()
            } catch {
              case e: Exception =>
                logger.error(s"Error parsing field_data JSON: ${e.getMessage}")
                e.printStackTrace()
            }
          }
        }

        resultSet.close()
        statement.close()
      } finally {
        connection.close()
      }
    } catch {
      case e: Exception =>
        logger.error(s"Error getting field sources for profile $profileId: ${e.getMessage}")
        e.printStackTrace()
    }

    result
  }

}
